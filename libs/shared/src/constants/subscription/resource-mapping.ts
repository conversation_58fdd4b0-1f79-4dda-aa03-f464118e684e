/**
 * Resource Type Mapping and Classification
 *
 * Consolidates the relationship between LimitationType and ResourceType
 * and defines which resources are plan-limited vs transactional.
 */

// Define the enums locally to avoid circular dependency
export enum LimitationType {
  CHECKS = 'CHECKS',
  TEAMS = 'TEAMS',
  MEMBERS = 'MEMBERS',
  INTEGRATIONS = 'INTEGRATIONS',
  STATUS_PAGES = 'STATUS_PAGES',
  SMS = 'SMS',
  PHONE_CALLS = 'PHONE_CALLS',
}

export enum ResourceType {
  MEMBER = 'MEMBER',
  TEAM = 'TEAM',
  CHECK = 'CHECK',
  INTEGRATION = 'INTEGRATION',
  STATUS_PAGE = 'STATUS_PAGE',
}

/**
 * Mapping between LimitationType and ResourceType for plan-limited resources
 * These resources have limits defined in subscription plans
 */
export const PLAN_RESOURCE_TYPE_MAPPING = {
  [LimitationType.MEMBERS]: ResourceType.MEMBER,
  [LimitationType.TEAMS]: ResourceType.TEAM,
  [LimitationType.CHECKS]: ResourceType.CHECK,
  [LimitationType.INTEGRATIONS]: ResourceType.INTEGRATION,
  [LimitationType.STATUS_PAGES]: ResourceType.STATUS_PAGE,
} as const

/**
 * Transactional resource types (globally priced, not plan-dependent)
 * These resources are billed per-use regardless of subscription plan
 */
export const TRANSACTIONAL_RESOURCE_TYPES = [
  LimitationType.SMS,
  LimitationType.PHONE_CALLS,
] as const

/**
 * Plan-limited resource types
 * These resources are limited by subscription plan quotas
 */
export const PLAN_LIMITED_RESOURCE_TYPES = [
  LimitationType.MEMBERS,
  LimitationType.TEAMS,
  LimitationType.CHECKS,
  LimitationType.INTEGRATIONS,
  LimitationType.STATUS_PAGES,
] as const

/**
 * Helper functions for resource classification
 */
export const isTransactionalResource = (
  limitationType: LimitationType,
): boolean => {
  return (TRANSACTIONAL_RESOURCE_TYPES as readonly LimitationType[]).includes(
    limitationType,
  )
}

export const isPlanLimitedResource = (
  limitationType: LimitationType,
): boolean => {
  return (PLAN_LIMITED_RESOURCE_TYPES as readonly LimitationType[]).includes(
    limitationType,
  )
}

export const getResourceTypeForLimitation = (
  limitationType: LimitationType,
): ResourceType | undefined => {
  return PLAN_RESOURCE_TYPE_MAPPING[
    limitationType as keyof typeof PLAN_RESOURCE_TYPE_MAPPING
  ]
}
