/**
 * Overage Policy Types
 *
 * Defines how the system handles resource usage that exceeds plan limits.
 * Used by billing logic in infrastructure Lambda functions and user-portal-backend.
 */
export enum OveragePolicyType {
  /**
   * Block further resource usage when limit is exceeded
   * User cannot create additional resources beyond their plan limit
   */
  BLOCK = 'block',

  /**
   * Allow usage beyond limit but charge for overage
   * Additional resources are billed according to overage pricing
   */
  CHARGE = 'charge',

  /**
   * Grace period - allow temporary overage without immediate billing
   * Used for plan transitions or special circumstances
   */
  GRACE = 'grace',
}
