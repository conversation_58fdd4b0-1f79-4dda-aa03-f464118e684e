/**
 * Usage Type Enum
 *
 * Defines all types of resource usage that can be tracked and billed.
 * Split into two categories:
 * - Transactional: Globally priced usage (SMS, phone calls)
 * - Resource Overage: Plan-specific resource overage billing
 *
 * Used by billing calculations in infrastructure and usage tracking in user-portal-backend.
 */
export enum UsageType {
  // Transactional usage types (globally priced)
  SMS = 'SMS',
  PHONE_CALL = 'PHONE_CALL',

  // Plan resource overage types (plan-specific pricing)
  MEMBER_OVERAGE = 'MEMBER_OVERAGE',
  TEAM_OVERAGE = 'TEAM_OVERAGE',
  CHECK_OVERAGE = 'CHECK_OVERAGE',
  INTEGRATION_OVERAGE = 'INTEGRATION_OVERAGE',
  STATUS_PAGE_OVERAGE = 'STATUS_PAGE_OVERAGE',
}

/**
 * Helper constants for categorizing usage types
 */
export const TRANSACTIONAL_USAGE_TYPES = [
  UsageType.SMS,
  UsageType.PHONE_CALL,
] as const

export const OVERAGE_USAGE_TYPES = [
  UsageType.MEMBER_OVERAGE,
  UsageType.TEAM_OVERAGE,
  UsageType.CHECK_OVERAGE,
  UsageType.INTEGRATION_OVERAGE,
  UsageType.STATUS_PAGE_OVERAGE,
] as const
