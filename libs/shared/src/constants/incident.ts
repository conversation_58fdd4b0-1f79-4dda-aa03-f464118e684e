export enum IncidentStatus {
  STARTED = 'STARTED',
  RECOVERED = 'RECOVERED',
  ISSUE_REAPPEARED = 'ISSUE_REAPPEARED',
  ACKNOWLEDGED = 'ACKNOWLEDGED',
  RESOLVED = 'RESOLVED',
}

export enum IncidentEvent {
  /**
   * When the worker detected an issue in check.
   */
  ISSUE_DETECTED = 'ISSUE_DETECTED',

  /**
   * When the incident passed the confirmation period.
   */
  STARTED = 'STARTED',

  /**
   * When workers do the check without error after the incident happens.
   */
  RECOVERED = 'RECOVERED',

  /**
   * When Orchestra<PERSON> starts waiting for the incident to auto-resolve.
   */
  WAIT_FOR_AUTO_RESOLVE = 'WAIT_FOR_AUTO_RESOLVE',

  /**
   * When the worker detects an issue, it is the same as the last issue.
   */
  REAPPEARED = 'REAPPEARED',

  /**
   * When the workers passed the check after the 'recovery period'.
   */
  RESOLVED = 'RESOLVED',
}

export enum IncidentEventType {
  CHECK = 'check',
  MANUAL = 'manual',
  SYSTEM = 'system',
  NOTIFICATION = 'notification',
  COMMENT = 'comment',
}

export enum IncidentEventDetailType {
  RECEIVED_ERROR = 'received_error',
  INCIDENT_STARTED = 'incident_started',
  ACKNOWLEDGED_INCIDENT = 'acknowledged_incident',
  INCIDENT_RECOVERED = 'incident_recovered',
  WAITING_AUTO_RESOLVE = 'waiting_auto_resolve',
  REAPPEARED = 'reappeared',
  RESOLVED = 'resolved',
  ESCALATED = 'escalated',
  SEND_SLACK = 'send_slack',
  SEND_PUSH_NOTIFICATION = 'send_push_notification',
  SEND_EMAIL = 'send_email',
  OPENED_EMAIL = 'opened_email',
  SEND_SMS = 'send_sms',
  CALL_CREATE = 'call_create',
  CALLING = 'calling',
  ANSWERED_CALL = 'answered_call',
  REJECT_CALL = 'reject_call',
  CALL_END = 'call_end',
  SEND_MESSAGE = 'send_message',
  COMMENT = 'comment',
  RECOVERED = 'recovered',
  START_RECOVERY = 'start_recovery',
  CALL_FAILED = 'call_failed',
  NO_ANSWER = 'no_answer',
}
