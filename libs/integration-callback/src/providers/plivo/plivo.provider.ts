import { IncidentEventDetailType } from '@libs/shared/constants/incident' // Assuming path based on project structure
import { IncidentIntegrationCallbackAction } from '@libs/integration-callback/incident-integration-callback/types'

import { PlivoCallbackPayload } from './types'

export class PlivoProvider {
  private actions: Partial<IncidentIntegrationCallbackAction>

  constructor(actions: Partial<IncidentIntegrationCallbackAction>) {
    this.actions = actions
  }

  /**
   * Handles the incoming callback payload from Plivo.
   * @param payload The parsed JSON payload from the Plivo callback.
   */
  async handleCallback(payload: PlivoCallbackPayload): Promise<void> {
    console.log('Handling Plivo callback:', payload)

    const { incidentId, userId, status } = payload

    if (!incidentId || !userId || !status) {
      console.error(
        'Invalid Plivo payload: Missing incidentId, userId, or status',
        payload,
      )
      // Consider throwing an error or returning early depending on desired behavior
      return
    }

    try {
      switch (status) {
        case 'answered':
          console.log(
            `Plivo call answered for incident ${incidentId}, user ${userId}. Acknowledging.`,
          )
          // Acknowledge the incident
          if (this.actions.acknowledgeIncident) {
            await this.actions.acknowledgeIncident({
              incidentId,
              userId: userId,
            })
          } else {
            console.warn('acknowledgeIncident action not provided')
          }

          // Log the answered event
          if (this.actions.logIncidentEvent) {
            await this.actions.logIncidentEvent({
              incidentId,
              userId: userId,
              type: IncidentEventDetailType.ANSWERED_CALL,
              value: { status, rawPayload: payload }, // Include raw payload for context
            })
          } else {
            console.warn('logIncidentEvent action not provided')
          }
          break

        case 'busy':
          console.log(
            `Plivo call failed (busy) for incident ${incidentId}, user ${userId}. Logging event.`,
          )
          if (this.actions.logIncidentEvent) {
            await this.actions.logIncidentEvent({
              incidentId,
              userId: userId,
              type: IncidentEventDetailType.CALL_FAILED, // Use the new type
              value: { status, reason: 'busy', rawPayload: payload },
            })
          } else {
            console.warn('logIncidentEvent action not provided')
          }
          break

        case 'timeout':
          console.log(
            `Plivo call timed out (no answer) for incident ${incidentId}, user ${userId}. Logging event.`,
          )
          if (this.actions.logIncidentEvent) {
            await this.actions.logIncidentEvent({
              incidentId,
              userId: userId,
              type: IncidentEventDetailType.NO_ANSWER, // Use the new type
              value: { status, reason: 'timeout', rawPayload: payload },
            })
          } else {
            console.warn('logIncidentEvent action not provided')
          }
          break

        default:
          console.warn(
            `Received unknown Plivo status '${status}' for incident ${incidentId}. Ignoring.`,
            payload,
          )
          break
      }
    } catch (error) {
      console.error(
        `Error processing Plivo callback for incident ${incidentId}:`,
        error,
      )
      // Re-throw or handle error appropriately
      throw error
    }
  }
}
