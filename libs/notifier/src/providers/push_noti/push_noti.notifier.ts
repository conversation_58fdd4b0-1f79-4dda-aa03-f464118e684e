import { PushNotiIncidentPayload } from '@libs/notifier/incident-notifier/incident-notifier.type'

class PushNotiNotifier {
  async send(payload: PushNotiIncidentPayload) {
    const headers = {
      Authorization: payload.token,
      accept: 'application/json',
      'Content-Type': 'application/json',
    }
    const response = await fetch('https://api.onesignal.com/notifications', {
      method: 'POST',
      headers,
      body: JSON.stringify({
        app_id: '3a3e2e86-3427-48c9-ac49-5b5f8f97982a', //static
        include_external_user_ids: payload.userId,
        contents: {
          en: `Your organization: ${payload.organizationName} just got new incident from ${payload.checkName}`,
        },
        headings: {
          en: 'Incident',
        },
        priority: 10, //static
        data: {
          userId: payload.userId,
          firebaseId: payload.firebaseId,
          teamId: payload.teamId,
          incidentId: payload.incidentId,
          organizationName: payload.organizationName,
          checkName: payload.checkName,
        },
        existing_android_channel_id: 'alarm_stream', //static
      }),
    })
    console.log(response)
    return
  }
}

export default PushNotiNotifier
