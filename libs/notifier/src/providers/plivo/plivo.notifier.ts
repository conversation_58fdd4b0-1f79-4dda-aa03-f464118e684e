import * as plivo from 'plivo'

export interface PlivoNotifierConfig {
  authId: string
  authToken: string
}

// Define the expected payload structure based on the agreed schema
export interface PhloPayload {
  incident_id: string
  incident_title: string
  incident_url: string
  incident_cause: string
  user_endpoint: string
  ack_callback_url?: string
  escalate_callback_url?: string
}

export class PlivoNotifier {
  private client: plivo.PhloClient

  constructor(config: PlivoNotifierConfig) {
    this.client = new plivo.PhloClient(config.authId, config.authToken, {})
  }

  async triggerPhlo(phloId: string, payload: PhloPayload): Promise<unknown> {
    console.log(`Triggering Plivo PHLO ${phloId} with payload:`, payload)
    try {
      const phlo = this.client.phlo(phloId)
      const response = await phlo.run(payload)
      console.log('Plivo PHLO API Response:', response)
      return response
    } catch (error) {
      console.error(`Error triggering Plivo PHLO ${phloId}:`, error)
      throw error // Re-throw the error for the caller to handle
    }
  }
}
