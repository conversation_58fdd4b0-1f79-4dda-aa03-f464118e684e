import IncidentNotifier<PERSON>rovider from '../incident-notifier.abstract'
import { PlivoNotifier } from '../../providers/plivo/plivo.notifier'
import { PlivoIncidentPayload } from '../incident-notifier.type'

export default class PlivoIncidentNotifier extends IncidentNotifierProvider<
  'voip_call',
  PlivoIncidentPayload
> {
  private plivoNotifier: PlivoNotifier

  constructor(plivoNotifier: PlivoNotifier) {
    super()
    this.plivoNotifier = plivoNotifier
  }

  private getUserEndpoint(endpoint: string): string {
    if (endpoint.includes('sip:')) {
      return endpoint
    }

    return `sip:${endpoint}@phone.plivo.com`
  }

  async started(payload: PlivoIncidentPayload): Promise<unknown> {
    const phloPayload = {
      from: payload.from,
      incident_id: payload.incidentId,
      incident_title: payload.title,
      incident_url: payload.incidentUrl,
      incident_cause: payload.cause,
      user_id: payload.userId,
      user_endpoint: this.getUserEndpoint(payload.userEndpoint),
      ack_callback_url: payload.ackCallbackUrl,
      escalate_callback_url: payload.escalateCallbackUrl,
    }

    return this.plivoNotifier.triggerPhlo(payload.phloId, phloPayload)
  }

  async resolved(payload: PlivoIncidentPayload): Promise<any> {
    // Placeholder: Implement logic for resolved incident notifications via Plivo if needed
    // Could trigger the same or a different PHLO with adjusted payload
    console.log(
      'PlivoIncidentNotifier: resolved() called, but not implemented yet.',
      payload,
    )
    return Promise.resolve()
  }
}
