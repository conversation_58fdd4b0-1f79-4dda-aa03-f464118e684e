import { NotifierProviderType } from '../@type/notifier.types'
import { EmailNotifierConfig } from '../providers/email'
import {
  PlivoNotifier,
  PlivoNotifierConfig,
} from '../providers/plivo/plivo.notifier' // Import Plivo provider

import { IncidentPayloadMap } from './incident-notifier.type'
import SlackIncidentNotifier from './slack/slack-incident-notifier'
import EmailIncidentNotifier from './email/email-incident-notifier'
import IncidentNotifierProvider from './incident-notifier.abstract'
// import VoiceCallIncidentNotifier from './voice-call/voice-call-incident-notifier' // Keep old one commented for reference if needed
import PushNotiIncidentNotifier from './push-noti/push-noti-incident-notifier'
import PlivoIncidentNotifier from './plivo/plivo-incident-notifier' // Import new Plivo notifier
import VoiceCallIncidentNotifier from './voice-call/voice-call-incident-notifier'
import SmsIncidentNotifier from './sms/sms-incident-notifier'

export type NotifierConfigMap = {
  slack: unknown
  email: EmailNotifierConfig
  voice_call: unknown
  voip_call: PlivoNotifierConfig
  push_noti: unknown
  sms: unknown
}

type ProviderMap = {
  slack: SlackIncidentNotifier
  email: EmailIncidentNotifier
  voice_call: VoiceCallIncidentNotifier
  voip_call: PlivoIncidentNotifier
  push_noti: PushNotiIncidentNotifier
  sms: SmsIncidentNotifier
}

class IncidentNotifier<P extends NotifierProviderType> {
  private _provider: IncidentNotifierProvider<P, IncidentPayloadMap[P]>
  constructor(providerType: P, config?: NotifierConfigMap[P]) {
    switch (providerType) {
      case 'slack':
        this._provider =
          new SlackIncidentNotifier() as IncidentNotifierProvider<
            P,
            IncidentPayloadMap[P]
          >
        break
      case 'email': {
        this._provider = new EmailIncidentNotifier(
          config as NotifierConfigMap['email'],
        ) as IncidentNotifierProvider<P, IncidentPayloadMap[P]>
        break
      }
      case 'voice_call': {
        this._provider =
          new VoiceCallIncidentNotifier() as IncidentNotifierProvider<
            P,
            IncidentPayloadMap[P]
          >
        break
      }
      case 'sms': {
        this._provider = new SmsIncidentNotifier() as IncidentNotifierProvider<
          P,
          IncidentPayloadMap[P]
        >
        break
      }

      case 'voip_call': {
        const plivoConfig = config as NotifierConfigMap['voip_call']
        const plivoProvider = new PlivoNotifier({
          authId: plivoConfig.authId,
          authToken: plivoConfig.authToken,
        })

        this._provider = new PlivoIncidentNotifier(
          plivoProvider,
        ) as IncidentNotifierProvider<P, IncidentPayloadMap[P]>
        break
      }

      case 'push_noti': {
        this._provider =
          new PushNotiIncidentNotifier() as IncidentNotifierProvider<
            P,
            IncidentPayloadMap[P]
          >
        break
      }
      default:
        throw new Error(`Unsupported provider: ${providerType}`)
    }
  }

  started(payload: IncidentPayloadMap[P]) {
    return this._provider.started(payload)
  }

  resolved(payload: IncidentPayloadMap[P]) {
    return this._provider.resolved(payload)
  }

  get provider() {
    return this._provider as ProviderMap[P]
  }
}

export default IncidentNotifier
