export interface IncidentPayload {
  incidentId: string

  incidentUrl: string // dashboard incident url
  checkUrl: string // dashboard incident url

  url: string // monitor url

  title: string
  startedAt: Date
  resolvedAt?: Date
  acknowledgedAt?: Date
  acknowledgedBy?: string
  cause: string
  method: string
}

export interface SlackIncidentPayload extends IncidentPayload {
  incomingWebhookUrl: string
}

export interface EmailIncidentPayload extends IncidentPayload {
  from: string
  to: { email: string; userId: string }[]
  headers?: { [key: string]: string }
}

export interface SmsIncidentPayload extends IncidentPayload {
  token: string
  from: string
  to: string
  userId: string
  firstName: string
  lastName: string
  serviceName: string
}

export interface VoiceCallIncidentPayload extends IncidentPayload {
  token: string
  from: string
  to: string
  userId: string
  firstName: string
  lastName: string
  serviceName: string
  IncidentEventCallbackUrl: string
  AckEscalateCallbackUrl: string
}

export interface PlivoIncidentPayload extends IncidentPayload {
  userEndpoint: string // Plivo SIP endpoint
  from: string // Plivo number
  userId: string
  phloId: string
  ackCallbackUrl?: string
  escalateCallbackUrl?: string
}

export interface PushNotiIncidentPayload extends IncidentPayload {
  token: string
  userId: string
  firebaseId: string
  teamId: string
  organizationName: string
  checkName: string
}

export type IncidentPayloadMap = {
  slack: SlackIncidentPayload
  email: EmailIncidentPayload
  voice_call: VoiceCallIncidentPayload
  push_noti: PushNotiIncidentPayload
  voip_call: PlivoIncidentPayload
  sms: SmsIncidentPayload
}

export interface IncidentWebhookPayload extends IncidentPayload {
  url: string
  request_method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  header_field: { [key: string]: string }
  basic_username?: string
  basic_password?: string
  request_body: { [key: string]: string }
}
