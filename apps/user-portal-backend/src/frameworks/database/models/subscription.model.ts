import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  Re<PERSON>,
} from 'typeorm'
import {
  SubscriptionStatus,
  OveragePolicyType,
} from '@libs/shared/constants/subscription'

import { AuditableSchema } from '@backend/frameworks/database/models/auditSchema'
import { Id } from '@backend/cores/base/id.type'
import { SubscriptionItemModel } from '@backend/frameworks/database/models/subscription-item.model'

import { OrganizationModel } from './organization.model'

@Entity('subscriptions')
export class SubscriptionModel extends AuditableSchema {
  @Column()
  organizationId: Id

  @Column({ nullable: true })
  stripeCustomerId: string

  @Column({ nullable: true })
  stripeSubscriptionId: string // This is a subscription ID from Stripe

  @Column({ type: 'varchar', default: SubscriptionStatus.INCOMPLETE })
  status: SubscriptionStatus

  @Column({ type: 'date', nullable: true })
  currentPeriodStartDate: Date

  @Column({ type: 'date', nullable: true })
  currentPeriodEndDate: Date

  @Column({ type: 'timestamp', nullable: true })
  canceledAt: Date

  @Column({ type: 'timestamp', nullable: true })
  gracePeriodEndsAt: Date

  @Column({ default: true })
  usageBillingEnabled: boolean

  @Column({ type: 'varchar', default: OveragePolicyType.CHARGE })
  overagePolicyFixedResources: OveragePolicyType

  @ManyToOne(
    () => OrganizationModel,
    (organization) => organization.subscriptions,
  )
  @JoinColumn({ name: 'organization_id' })
  organization: Relation<OrganizationModel>

  @OneToMany(
    () => SubscriptionItemModel,
    (subscriptionItem) => subscriptionItem.subscription,
  )
  subscriptionItems: Relation<SubscriptionItemModel[]>
}
