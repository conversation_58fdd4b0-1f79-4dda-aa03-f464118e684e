import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, Relation } from 'typeorm'
import {
  PlanType,
  SubscriptionStatus,
} from '@libs/shared/constants/subscription'
import { ResourceType } from '@libs/shared/constants/subscription/resource-mapping'

import { AuditableSchema } from '@backend/frameworks/database/models/auditSchema'
import { SubscriptionPlanModel } from '@backend/frameworks/database/models/subscription-plan.model'
import { Id } from '@backend/cores/base/id.type'
import { SubscriptionModel } from '@backend/frameworks/database/models/subscription.model'

@Entity('subscription_items')
export class SubscriptionItemModel extends AuditableSchema {
  @Column()
  subscriptionId: Id

  @Column({ nullable: true })
  stripeSubscriptionItemId: string

  @Column()
  stripePriceId: string

  @Column()
  subscriptionPlanId: Id

  @Column({ type: 'varchar' })
  itemType: PlanType

  @Column({ default: 1 })
  quantity: number

  @Column({
    type: 'varchar',
    default: SubscriptionStatus.ACTIVE,
  })
  status: SubscriptionStatus

  @Column({ type: 'date' })
  startDate: Date

  @Column({ type: 'date' })
  endDate: Date

  @Column({ type: 'varchar', nullable: true })
  resourceType: ResourceType | null

  @ManyToOne(
    () => SubscriptionModel,
    (subscription) => subscription.subscriptionItems,
  )
  @JoinColumn({ name: 'subscription_id' })
  subscription: Relation<SubscriptionModel>

  @ManyToOne(() => SubscriptionPlanModel)
  @JoinColumn({ name: 'subscription_plan_id' })
  subscriptionPlan: Relation<SubscriptionPlanModel>
}
