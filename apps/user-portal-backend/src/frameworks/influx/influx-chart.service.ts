import { Id } from '@backend/cores/base/id.type'

import { InfluxService } from './influx.service'
import { Measurement } from './constants/influx.constant'

class InfluxChartService extends InfluxService {
  private bucket = 'monitoringdog-dev'
  async queryResponseTimeData(
    checkId: Id,
    responseTimeQuery: {
      workerId: string
      groupTime: string
      percentile: number
      measurement: keyof typeof Measurement
      timeRange: string
    },
  ): Promise<
    {
      time: Date
      avgTcpConnection: number
      avgTlsHandshake: number
      avgTotalTime: number
    }[]
  > {
    const { groupTime, percentile, measurement, timeRange, workerId } =
      responseTimeQuery
    if (timeRange === '24h') this.bucket = 'monitoringdog-dev'
    else this.bucket = 'monitoringdog-dev-week'
    const query = `
    SELECT
        DATE_BIN(INTERVAL '${groupTime}', time, '1970-01-01T00:00:00Z'::TIMESTAMP) AS _time,
        ROUND(APPROX_PERCENTILE_CONT(tcp_connection, ${percentile}), 1) AS avg_tcp_connection,
        ROUND(APPROX_PERCENTILE_CONT(tls_handshake, ${percentile}), 1) AS avg_tls_handshake,
        ROUND(APPROX_PERCENTILE_CONT(data_transfer, ${percentile}), 1) AS avg_data_transfer,
        ROUND(APPROX_PERCENTILE_CONT(total_time, ${percentile}), 1) AS avg_total_time
    FROM "${Measurement[measurement]}"
    WHERE
        time >= now() - interval '${timeRange}'
        AND "check_id" = '${checkId}' AND "worker" = '${workerId}'
    GROUP BY
        _time
      `
    const result = await this.query(query, 'sql', this.bucket)

    const dataPoints: {
      time: Date
      avgTcpConnection: number
      avgTlsHandshake: number
      avgDataTransfer: number
      avgTotalTime: number
    }[] = []

    for await (const row of result) {
      dataPoints.push({
        time: new Date(row['_time']),
        avgTcpConnection: Number(row['avg_tcp_connection']),
        avgTlsHandshake: Number(row['avg_tls_handshake']),
        avgDataTransfer: Number([row['avg_data_transfer']]),
        avgTotalTime: Number(row['avg_total_time']),
      })
    }

    return dataPoints
  }

  async getUpDownData(
    checkId: Id,
    upDownRequest: {
      groupTime: string
      timeRange: string
      field: string
      fieldResult: string
      measurement: keyof typeof Measurement
    },
  ): Promise<{ time: Date; successPercentage: number }[]> {
    const { groupTime, timeRange, field, fieldResult, measurement } =
      upDownRequest
    // if (timeRange === '24h') this.bucket = 'monitoringdog-dev'
    // else this.bucket = 'monitoringdog-dev-week'
    const query = `
      SELECT
          DATE_BIN('${groupTime}', time) AS _time,
          (COUNT(CASE WHEN "${field}" = '${fieldResult}' THEN 1 END) * 100.0 / COUNT(*)) AS ${fieldResult}_percentage
      FROM "${Measurement[measurement]}"
      WHERE
        time >= now() - interval '${timeRange}'
        AND ("${field}" IS NOT NULL)
        AND "check_id" IN ('${checkId}')
      GROUP BY
        _time
      ORDER BY
        _time;
      `
    const result = await this.query(query, 'sql', this.bucket)
    const dataPoints: {
      time: Date
      successPercentage: number
    }[] = []

    for await (const row of result) {
      dataPoints.push({
        time: new Date(row['_time']),
        successPercentage: Number(row['success_percentage']),
      })
    }

    return dataPoints
  }
}

export { InfluxChartService }
