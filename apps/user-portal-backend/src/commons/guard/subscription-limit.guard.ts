import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common'
import { Reflector } from '@nestjs/core'
import { LimitationType } from '@libs/shared/constants/subscription'

import { SubscriptionLimitService } from '@backend/modules/subscription/services/subscription-limit.service'

import { SUBSCRIPTION_LIMIT_KEY } from '../decorators/check-subscription-limit.decorator'
import { OrgTeamHeadersDto } from '../dto/org-team-headers.dto'

interface SubscriptionLimitMetadata {
  limitationType: LimitationType
  countFromRequestKey?: string
}

@Injectable()
export class SubscriptionLimitGuard implements CanActivate {
  private readonly logger = new Logger(SubscriptionLimitGuard.name)

  constructor(
    private reflector: Reflector,
    private subscriptionLimitService: SubscriptionLimitService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const metadata =
      this.reflector.getAllAndOverride<SubscriptionLimitMetadata>(
        SUBSCRIPTION_LIMIT_KEY,
        [context.getHandler(), context.getClass()],
      )

    if (!metadata) {
      return true
    }

    const request = context.switchToHttp().getRequest()
    const orgTeamHeadersDto = new OrgTeamHeadersDto()
    orgTeamHeadersDto.organizationId = request.headers['organization-id']

    if (!orgTeamHeadersDto.organizationId) {
      throw new UnauthorizedException('Organization ID is required')
    }

    const { limitationType, countFromRequestKey } = metadata
    const count = this.calculateCount(request, countFromRequestKey)

    await this.subscriptionLimitService.checkLimit(
      orgTeamHeadersDto.organizationId,
      limitationType,
      count,
    )
    return true
  }

  private calculateCount(request: any, countFromRequestKey?: string): number {
    let count = 1
    if (countFromRequestKey) {
      const countSource = request.body ?? request.query ?? request.params
      const rawCount = countSource
        ? countSource[countFromRequestKey]
        : undefined
      if (rawCount !== undefined) {
        const parsedCount = parseInt(String(rawCount), 10)
        if (!isNaN(parsedCount)) {
          count = parsedCount
        }
      }
    }
    return count
  }
}
