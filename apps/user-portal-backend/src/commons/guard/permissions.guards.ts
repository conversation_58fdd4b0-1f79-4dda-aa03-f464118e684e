// guards/permissions.guard.ts
import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common'
import { Reflector } from '@nestjs/core'

import CaslService from '@backend/cores/rbac/casl.service'
import { PERMISSION_QUERIES_KEY } from '@backend/cores/rbac/permission-queries'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import {
  PermissionAction,
  PermissionScope,
  PermissionType,
} from '@backend/cores/rbac/permission.constant'

import { CHECK_PERMISSIONS_KEY } from '../decorators/check-permission.decorator'
import { OrgTeamHeadersDto } from '../dto/org-team-headers.dto'

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private caslService: CaslService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredPermissions = this.reflector.getAllAndOverride<any[]>(
      CHECK_PERMISSIONS_KEY,
      [context.getHandler(), context.getClass()],
    ) as {
      action: PermissionAction
      scope: PermissionScope
      type?: PermissionType
    }[]

    if (!requiredPermissions) {
      return true
    }

    const request = context.switchToHttp().getRequest()
    const user = request.user as JwtPayload

    if (!user) return false

    request[PERMISSION_QUERIES_KEY] = await this.caslService.verifyPermissions(
      user.userId,
      requiredPermissions,
    )

    // Get organizationId and teamId from headers
    const orgTeamHeadersDto = new OrgTeamHeadersDto()
    orgTeamHeadersDto.organizationId = request.headers['organization-id']
    orgTeamHeadersDto.teamId = request.headers['team-id']

    const permit = this.checkPermissions(
      request[PERMISSION_QUERIES_KEY],
      orgTeamHeadersDto,
      requiredPermissions,
    )

    return permit
  }

  // Since the CASL library return query conditions as MongoDB query-liked,
  // Implement a custom function to check the permissions. Handle $or, $and, and simple queries
  private checkPermissions(
    permissionQueries: any,
    orgTeamHeadersDto: OrgTeamHeadersDto,
    requiredPermissions: {
      action: PermissionAction
      scope: PermissionScope
      type?: PermissionType
    }[],
  ): boolean {
    if ('$or' in permissionQueries) {
      return permissionQueries.$or.some((query: any) =>
        this.checkPermissions(query, orgTeamHeadersDto, requiredPermissions),
      )
    }

    if ('$and' in permissionQueries) {
      return permissionQueries.$and.every((query: any) =>
        this.checkPermissions(query, orgTeamHeadersDto, requiredPermissions),
      )
    }

    return requiredPermissions.every(({ type }) => {
      switch (type) {
        case PermissionType.ORGANIZATION:
          return (
            permissionQueries.organizationId ===
            orgTeamHeadersDto.organizationId
          )
        case PermissionType.TEAM:
          return (
            permissionQueries.teamId === orgTeamHeadersDto.teamId &&
            permissionQueries.organizationId ===
              orgTeamHeadersDto.organizationId
          )
        default:
          return true
      }
    })
  }
}
