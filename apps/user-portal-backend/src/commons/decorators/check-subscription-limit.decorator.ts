import { UseGuards, applyDecorators, SetMetadata } from '@nestjs/common'
import { LimitationType } from '@libs/shared/constants/subscription'

import { SubscriptionLimitGuard } from '../guard/subscription-limit.guard'

export const SUBSCRIPTION_LIMIT_KEY = 'subscription_limit'

export function CheckSubscriptionLimit(
  limitationType: LimitationType,
  countFromRequestKey?: string,
) {
  return applyDecorators(
    SetMetadata(SUBSCRIPTION_LIMIT_KEY, {
      limitationType,
      countFromRequestKey,
    }),
    UseGuards(SubscriptionLimitGuard),
  )
}
