// eslint-disable-next-line @typescript-eslint/no-var-requires
require('dotenv').config({
  path: 'apps/user-portal-backend/.env',
})

import { DynamooseModuleOptions } from 'nestjs-dynamoose'
import * as dynamoose from 'dynamoose'
import { DataSourceOptions } from 'typeorm'
import { SnakeNamingStrategy } from 'typeorm-naming-strategies'
import {
  ExtractJwt,
  StrategyOptionsWithRequest,
  StrategyOptionsWithoutRequest,
} from 'passport-jwt'
import { SQSClient } from '@aws-sdk/client-sqs'

import * as path from 'path'

class ConfigService {
  constructor(private env: { [k: string]: string | undefined }) {
    if (
      process.env.SECRETS &&
      Object.keys(JSON.parse(process.env.SECRETS)).length
    ) {
      const secrets = JSON.parse(process.env.SECRETS)
      Object.entries(secrets).forEach(([key, value]) => {
        // @ts-expect-error: we will inject key later on?
        process.env[key] = value
      })
    }
  }

  public getValue(key: string, throwOnMissing = false): string {
    const value = this.env[key]
    if (!value && throwOnMissing) {
      throw new Error(`config error - missing env.${key}`)
    }

    return value as string
  }

  public ensureValues(keys: string[]) {
    keys.forEach((k) => this.getValue(k, true))
    return this
  }

  public isProduction() {
    const mode = this.getValue('NODE_ENV', true) || 'development'
    return mode !== 'development'
  }

  public getTypeOrmConfig(): DataSourceOptions {
    return {
      type: 'postgres',
      host: this.getValue('DB_HOST'),
      port: parseInt(this.getValue('DB_PORT')),
      username: this.getValue('DB_USER'),
      password: this.getValue('DB_PASSWORD'),
      database: this.getValue('DB_NAME'),
      poolSize: Number(this.getValue('POOL_SIZE') || 5),
      migrationsTableName: 'migration',
      synchronize: false, // Disabled to use migrations instead

      namingStrategy: new SnakeNamingStrategy(),

      ssl: false,

      // logging: true,

      // Cache configuration
      cache: {
        type: 'ioredis',
        options: {
          ...this.getRedisConfig(),
          // ioredis options
          options: {
            redisOptions: {
              maxRetriesPerRequest: 5,
            },
          },
        },
        ignoreErrors: true,

        // Default cache for 24 hours in milliseconds
        duration: 24 * 60 * 60 * 1000,
      },
    }
  }

  public getFirebaseConfig() {
    return {
      googleApplicationCredential: `credentials/firebase-${this.getValue('ENV')}.json`,
    }
  }

  public getJWTSecretSignOptions() {
    return {
      secret: this.getValue('JWT_SECRET'),
      expiresIn: '1d',
    }
  }

  public getJWTSecretSignMobileOptions() {
    return {
      secret: this.getValue('JWT_SECRET'),
      expiresIn: '30d',
    }
  }

  public getInfluxConfig() {
    return {
      url: this.getValue('INFLUX_URL'),
      token: this.getValue('INFLUX_TOKEN'),
      bucket: this.getValue('BUCKET'),
    }
  }

  public getRedisConfig() {
    return {
      host: this.getValue('REDIS_HOST'),
      port: parseInt(this.getValue('REDIS_PORT')),
    }
  }

  public getJWTRefreshSecretSignOptions() {
    return {
      secret: this.getValue('JWT_REFRESH_SECRET'),
      expiresIn: '30d',
    }
  }

  public getJWTRefreshSecretSignMobileOptions() {
    return {
      secret: this.getValue('JWT_REFRESH_SECRET'),
      expiresIn: '90d',
    }
  }

  public getJWTConfig(
    type: 'token' | 'refreshToken',
  ): StrategyOptionsWithRequest | StrategyOptionsWithoutRequest {
    switch (type) {
      case 'token':
        return {
          secretOrKey: this.getValue('JWT_SECRET'),
          jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
          passReqToCallback: false,
        } as StrategyOptionsWithoutRequest

      case 'refreshToken':
        return {
          secretOrKey: this.getValue('JWT_REFRESH_SECRET'),
          jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
          passReqToCallback: true,
        } as StrategyOptionsWithRequest
    }
  }

  public getSESConfig() {
    return {
      transport: {
        host: this.getValue('SES_HOST'),
        port: Number(this.getValue('SES_PORT')),
        ignoreTLS: false,
        secure: false,
        auth: {
          user: this.getValue('SES_SMTP_USERNAME'),
          pass: this.getValue('SES_SMTP_PASSWORD'),
        },
      },
      templateFolder: path.join(__dirname, 'resources/templates/'),
    }
  }

  public getSqsClient() {
    return new SQSClient({
      region: this.getValue('AWS_REGION'),
      credentials: {
        accessKeyId: this.getValue('AWS_ACCESS_KEY'),
        secretAccessKey: this.getValue('AWS_SECRET_ACCESS_KEY'),
        accountId: this.getValue('AWS_ACCOUNT_ID'),
      },
    })
  }

  public getDynamooseOptions(): DynamooseModuleOptions {
    const isProduction = this.isProduction()
    if (this.getValue('LOCALSTACK_URL', false) && !isProduction) {
      // LocalStack/Development configuration
      console.log('Using LocalStack DynamoDB configuration')

      return {
        aws: {
          region: 'us-east-1',
          accessKeyId: 'test',
          secretAccessKey: 'test',
        },
        local:
          this.getValue('LOCALSTACK_URL', false) || 'http://localhost:4566',
        logger: true,
        table: {
          throughput: 'ON_DEMAND' as const,
        },
      }
    } else {
      // Production configuration
      console.log('Using Production DynamoDB configuration')
      return {
        ddb: new dynamoose.aws.ddb.DynamoDB({
          credentials: {
            accessKeyId: this.getAwsConfig().accessKeyId,
            secretAccessKey: this.getAwsConfig().secretAccessKey,
          },
          region: this.getAwsConfig().region,
        }),
        logger: false, // Disable verbose logging in production
        table: {
          create: false, // Do not auto-create tables in production
          throughput: 'ON_DEMAND' as const,
        },
      }
    }
  }

  public getAwsConfig() {
    return {
      accessKeyId: this.getValue('AWS_ACCESS_KEY'),
      secretAccessKey: this.getValue('AWS_SECRET_ACCESS_KEY'),
      region: this.getValue('AWS_REGION'),
      accountId: this.getValue('AWS_ACCOUNT_ID'),
      webhookSQSURL: this.getValue('WEBHOOK_SQS_URL'),
    }
  }

  public getEncryptionConfig() {
    return {
      enc: this.getValue('SECRET_TOKEN'),
    }
  }

  public getPlivoConfig() {
    return {
      plivoAuthId: this.getValue('PLIVO_AUTH_ID'),
      plivoAuthToken: this.getValue('PLIVO_AUTH_TOKEN'),
      plivoAppId: this.getValue('PLIVO_APP_ID'),
    }
  }

  public getOneSignalConfig() {
    return {
      appId: this.getValue('ONESIGNAL_APP_ID'),
      apiKey: this.getValue('ONESIGNAL_API_KEY'),
    }
  }
}

const configService = new ConfigService(process.env).ensureValues([
  'DB_HOST',
  'DB_PORT',
  'DB_USER',
  'DB_PASSWORD',
  'DB_NAME',
  'JWT_SECRET',
  'JWT_REFRESH_SECRET',
  'SES_HOST',
  'SES_PORT',
  'SES_SMTP_USERNAME',
  'SES_SMTP_PASSWORD',
  'REDIS_HOST',
  'REDIS_PORT',
  'WEB_PORTAL_EMAIL_AUTH',
  'AWS_ACCESS_KEY',
  'AWS_SECRET_ACCESS_KEY',
  'AWS_REGION',
  'AWS_ACCOUNT_ID',
  'SLACK_CLIENT_ID',
  'SLACK_CLIENT_SECRET',
  'SECRET_TOKEN',
  'PLIVO_AUTH_ID',
  'PLIVO_AUTH_TOKEN',
  'PLIVO_APP_ID',
  'ONESIGNAL_APP_ID',
  'ONESIGNAL_API_KEY',
  'STRIPE_SECRET_KEY',
  'STRIPE_WEBHOOK_SECRET',
])

export { configService }
