import { Inject, Injectable, forwardRef } from '@nestjs/common'

import { Organization, Role, Team, User } from '@backend/modules/user/entities'
import OrganizationRepository, {
  ORGANIZATION_REPOSITORY,
} from '@backend/modules/user/applications/organization.repository'
import { SeverityUseCase } from '@backend/modules/escalation/usecases/severity.usecase'
import { Id } from '@backend/cores/base/id.type'
import { OrganizationResponseDto } from '@backend/modules/user/applications/dto/organization.response.dto'
import TeamRepository, {
  TEAM_REPOSITORY,
} from '@backend/modules/user/applications/team.repository'
import {
  PermissionAction,
  PermissionScope,
  PermissionType,
} from '@backend/cores/rbac/permission.constant'
import UserRepository, {
  USER_REPOSITORY,
} from '@backend/modules/user/applications/users.repository'
import PermissionRepository, {
  PERMISSION_REPOSITORY,
} from '@backend/modules/user/applications/permission.repository'
import RoleRepository, {
  ROLE_REPOSITORY,
} from '@backend/modules/user/applications/role.repository'

import {
  CreateOrganizationDto,
  UpdateOrganizationDto,
} from '../applications/dto/orgnization.request.dto'

@Injectable()
export class OrganizationUseCase {
  constructor(
    @Inject(ORGANIZATION_REPOSITORY)
    private readonly organizationRepository: OrganizationRepository,
    @Inject(TEAM_REPOSITORY)
    private readonly teamRepository: TeamRepository,
    @Inject(USER_REPOSITORY)
    private readonly userRepository: UserRepository,
    @Inject(PERMISSION_REPOSITORY)
    private readonly permissionRepository: PermissionRepository,
    @Inject(ROLE_REPOSITORY)
    private readonly roleRepository: RoleRepository,
    @Inject(forwardRef(() => SeverityUseCase))
    private readonly severityUsecase: SeverityUseCase,
  ) {}

  async create(
    createOrganizationDto: CreateOrganizationDto,
    userId: string,
  ): Promise<OrganizationResponseDto> {
    const currentUser = await this.userRepository.findById(userId, {
      relations: {
        organizations: true,
      },
    })
    if (!currentUser) {
      throw new Error('User not found')
    }

    // Create organization
    const org: Organization = Organization.create({
      name: createOrganizationDto.name,
      ownerId: createOrganizationDto.ownerId,
      teams: [],
    })
    await this.organizationRepository.create(org)

    // Create default role
    const adminPermissions = await this.permissionRepository.findAll({
      filter: {
        action: PermissionAction.MANAGE,
      },
    })

    const viewerPermissions = await this.permissionRepository.findAll({
      filter: {
        scope: {
          $in: [
            PermissionScope.TEAM_SETTINGS,
            PermissionScope.TEAM_MEMBERS,
            PermissionScope.CHECKS,
            PermissionScope.INCIDENTS,
          ],
        },
        type: {
          $in: [PermissionType.TEAM],
        },
        action: PermissionAction.VIEW,
      },
    })

    const adminRole = Role.generateRole({
      organizationId: org.id,
      roleName: 'Admin',
      permissions: adminPermissions,
      isDefault: true,
    })

    const viewerRole = Role.generateRole({
      organizationId: org.id,
      roleName: 'Viewer',
      permissions: viewerPermissions,
      isDefault: true,
    })
    console.log('creating severity')
    await this.roleRepository.createMany([adminRole, viewerRole])

    // Create default team
    const team: Team = await this.teamRepository.create(
      Team.create({
        name: 'My Team',
        organization: org,
      }),
    )

    const orgs = currentUser.getProps().organizations || []
    orgs.push(org)
    const updateUserReq = User.update({
      id: userId,
      props: {
        organizations: orgs,
      },
    })

    await this.userRepository.assignRole(currentUser, {
      role: adminRole,
      team: team,
    })

    await this.userRepository.updateById(userId, updateUserReq.getProps())

    // Create default severities for the team
    await this.severityUsecase.createDefaultSeverity(userId, team.id)
    return org.toResponse()
  }

  findAll() {
    return this.organizationRepository.findAll()
  }

  findOne(id: Id) {
    return this.organizationRepository
      .findById(id)
      .then((organization) => organization?.toResponse())
  }

  update(id: Id, updateOrganizationDto: UpdateOrganizationDto) {
    return this.organizationRepository.updateById(id, updateOrganizationDto)
  }

  remove(id: Id) {
    return this.organizationRepository.delete(id)
  }

  async findAllByUserId(userId: string) {
    const orgs = await this.organizationRepository.findByUserId(userId)
    return orgs?.map((org) => org.toResponse())
  }

  async findOrgByTeamId(teamId: string) {
    const team = await this.teamRepository.findById(teamId, {
      relations: {
        organization: true,
      },
    })
    return team?.organization?.toResponse().id
  }
}
