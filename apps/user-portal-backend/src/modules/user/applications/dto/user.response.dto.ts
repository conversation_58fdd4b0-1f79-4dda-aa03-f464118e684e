import { Id } from '@backend/cores/base/id.type'

import {
  UserResponse,
  RoleResponse,
  TeamResponse,
  PermissionResponse,
  OrganizationResponse,
} from '../../entities'
import { InternalRole } from '../../constants/user'

export class UserResponseDto implements UserResponse {
  id: Id
}

export class RoleResponseDto implements RoleResponse {
  id: Id
  permissions: PermissionResponse[]
}

export class CurrentUserResponseDto implements UserResponse {
  id: string
  firebaseId?: string
  firstName?: string
  lastName?: string
  email?: string
  phoneNumber?: string
  lastLoginAt?: Date
  lastLoginMethod?: string
  createdAt?: Date
  updatedAt?: Date
  internalRole?: InternalRole
  organizations?: {
    id: OrganizationResponse['id']
    name: OrganizationResponse['name']
    teams?: {
      id: TeamResponse['id']
      name: TeamResponse['name']
      role?: {
        id: RoleResponse['id']
        name: RoleResponse['name']
        permissions: PermissionResponse[]
      }
    }[]
  }[]
}

export class UserAuthResponseDto implements UserResponse {
  id: Id
  firebaseId: string
  organizations?: {
    id: OrganizationResponse['id']
    teams?: {
      id: TeamResponse['id']
      role?: {
        id: RoleResponse['id']
        permissions: PermissionResponse[]
      }
    }[]
  }[]
}
