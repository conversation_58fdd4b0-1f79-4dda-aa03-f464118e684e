import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common'
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger'
import { LimitationType } from '@libs/shared/constants/subscription'

import { CurrentUser } from '@backend/commons/decorators/current-user.decorator'
import { AuthGuard } from '@backend/commons/guard/auth.guard'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { TeamUseCase } from '@backend/modules/user/usecases/team.usecase'
import {
  CreateTeamDto,
  UpdateTeamDto,
  UpdateTeamMember,
} from '@backend/modules/user/applications/dto/team.request.dto'
import { PermissionsGuard } from '@backend/commons/guard/permissions.guards'
import { CheckPermissions } from '@backend/commons/decorators/check-permission.decorator'
import {
  PermissionAction,
  PermissionScope,
  PermissionType,
} from '@backend/cores/rbac/permission.constant'
import { ApiPaginatedResponse } from '@backend/commons/decorators/paginate-response.decorator'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'
import { OrgTeamHeaders } from '@backend/commons/decorators/org-team-headers.decorator'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { PaginateDto } from '@backend/commons/dto/paginate.dto'
import { CheckSubscriptionLimit } from '@backend/commons/decorators/check-subscription-limit.decorator'

import {
  TeamMemberResponseDto,
  TeamResponseDto,
} from '../applications/dto/team.response.dto'

@ApiTags('Team')
@Controller('team')
export class TeamController {
  constructor(private readonly teamUseCase: TeamUseCase) {}

  @Post()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.TEAM_CREATE,
      type: PermissionType.ORGANIZATION,
    },
  ])
  @CheckSubscriptionLimit(LimitationType.TEAMS)
  create(
    @CurrentUser() user: JwtPayload,
    @Body() createTeamDto: CreateTeamDto,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    return this.teamUseCase.create(createTeamDto, user, orgTeamParams)
  }

  @Get()
  @ApiBearerAuth()
  @ApiPaginatedResponse(TeamResponseDto)
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.TEAM_LISTING,
      type: PermissionType.ORGANIZATION,
    },
  ])
  find(
    @Query() paginateOption: PaginateOptionsDto,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ): Promise<PaginateDto<TeamResponseDto>> {
    return this.teamUseCase.find(orgTeamParams, paginateOption)
  }

  @Get(':id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.TEAM_LISTING,
      type: PermissionType.ORGANIZATION,
    },
  ])
  findOne(@Param('id') id: string) {
    return this.teamUseCase.findOne(id)
  }

  @Get(':id/members')
  @ApiBearerAuth()
  @ApiPaginatedResponse(TeamMemberResponseDto)
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.TEAM_MEMBERS,
      type: PermissionType.TEAM,
    },
  ])
  findMembers(
    @Query() paginateOption: PaginateOptionsDto,
    @Param('id') teamId: string,
  ) {
    return this.teamUseCase.findMembers(teamId, paginateOption)
  }

  @Get(':id/all-members')
  @ApiBearerAuth()
  @ApiPaginatedResponse(TeamMemberResponseDto)
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.TEAM_MEMBERS,
      type: PermissionType.TEAM,
    },
  ])
  findAllMembers(@Param('id') teamId: string) {
    return this.teamUseCase.findAllMembers(teamId)
  }

  @Delete(':id/member/:userId')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.TEAM_SETTINGS,
      type: PermissionType.TEAM,
    },
  ])
  removeMember(@Param('id') teamId: string, @Param('userId') userId: string) {
    return this.teamUseCase.removeMember(teamId, userId)
  }

  @Patch(':id/member')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.TEAM_SETTINGS,
      type: PermissionType.TEAM,
    },
  ])
  updateMember(
    @Param('id') teamId: string,
    @Body() updateTeamMember: UpdateTeamMember,
  ) {
    return this.teamUseCase.updateMember(teamId, updateTeamMember)
  }

  @Get(':id/invites')
  @ApiBearerAuth()
  @ApiPaginatedResponse(TeamMemberResponseDto)
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.TEAM_MEMBERS,
      type: PermissionType.TEAM,
    },
  ])
  findInvites(
    @Query() paginateOption: PaginateOptionsDto,
    @Param('id') teamId: string,
  ) {
    return this.teamUseCase.findInvites(teamId, paginateOption)
  }

  @Patch(':id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.TEAM_SETTINGS,
      type: PermissionType.TEAM,
    },
  ])
  update(@Param('id') id: string, @Body() updateTeamDto: UpdateTeamDto) {
    return this.teamUseCase.update(id, updateTeamDto)
  }

  @Delete(':id')
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.TEAM_SETTINGS,
      type: PermissionType.TEAM,
    },
  ])
  remove(@Param('id') id: string) {
    return this.teamUseCase.remove(id)
  }
}
