import { createZodDto } from '@anatine/zod-nestjs'
import { extendZodWithOpenApi } from '@anatine/zod-openapi'
import { z } from 'zod'

// Extend Zod with OpenAPI capabilities
extendZodWithOpenApi(z)

const CreateCheckoutSessionZodSchema = z.object({
  planId: z
    .string()
    .min(1)
    .describe('The ID of the subscription plan to create checkout session for.')
    .openapi({ example: 'plan-123' }),
  type: z
    .enum(['subscription', 'addons'])
    .describe('The type of checkout session.'), // from ApiProperty
  quantity: z
    .number() // from IsNumber
    .describe('The quantity for the checkout session.'), // from ApiProperty
})

export class CreateCheckoutSessionDto extends createZodDto(
  CreateCheckoutSessionZodSchema,
) {}
