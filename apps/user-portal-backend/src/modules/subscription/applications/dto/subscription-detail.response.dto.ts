import { createZodDto } from '@anatine/zod-nestjs'
import { extendApi } from '@anatine/zod-openapi'
import { z } from 'zod'

import {
  SubscriptionPlanResponse,
  SubscriptionUsageResponse,
  SubscriptionItemResponse,
  SubscriptionResponse,
} from '@backend/modules/subscription/entities'

export class SubscriptionDetailResponseDto extends createZodDto(
  extendApi(SubscriptionResponse).extend({
    subscriptionItems: z.array(SubscriptionItemResponse).optional(),
    planDetails: SubscriptionPlanResponse.optional().nullable(),
    usageHistory: z.array(SubscriptionUsageResponse).optional(),
    usageBilling: z
      .object({
        currentPeriod: z.object({
          startDate: z.date(),
          endDate: z.date(),
          sms: z.object({
            count: z.number(),
            cost: z.number(), // cost in cents
          }),
          phoneCalls: z.object({
            count: z.number(),
            cost: z.number(), // cost in cents
          }),
          totalCost: z.number(), // cost in cents
        }),
        previousPeriod: z
          .object({
            startDate: z.date(),
            endDate: z.date(),
            sms: z.object({
              count: z.number(),
              cost: z.number(), // cost in cents
            }),
            phoneCalls: z.object({
              count: z.number(),
              cost: z.number(), // cost in cents
            }),
            totalCost: z.number(), // cost in cents
          })
          .optional(),
      })
      .optional(),
    resourceLimits: z
      .array(
        z.object({
          resourceType: z.string(),
          includedQuantity: z.number(),
          currentUsage: z.number(),
          overage: z.number(),
          overagePrice: z.number().optional(), // price in cents
          stripePriceId: z.string().optional(),
        }),
      )
      .optional(),
  }),
) {}
