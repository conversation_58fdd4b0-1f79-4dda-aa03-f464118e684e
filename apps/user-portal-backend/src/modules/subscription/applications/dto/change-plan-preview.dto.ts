import { IsString, IsNotEmpty, IsUUID } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'

export class ChangePlanPreviewRequestDto {
  @ApiProperty({
    description: 'The ID of the new subscription plan to preview change to',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty()
  @IsString()
  @IsUUID()
  newPlanId: string
}

export class ChangePlanPreviewResponseDto {
  @ApiProperty({
    description: 'Immediate charges that would be applied (amounts in cents)',
    type: Object,
    example: {
      overageCharges: { members: 5000, teams: 2000 },
      proration: { credit: -500, debit: 1000 },
      totalAmountDue: 7500,
    },
  })
  immediateCharges: {
    overageCharges: { [key: string]: number } // e.g., { members: 5000 } (in cents)
    proration: {
      credit: number // e.g., -500 (in cents)
      debit: number // e.g., 1000 (in cents)
    }
    totalAmountDue: number // e.g., 5500 (in cents)
  }

  @ApiProperty({
    description: 'Information about the next invoice (amounts in cents)',
    type: Object,
    example: {
      nextInvoiceDate: '2024-02-01T00:00:00.000Z',
      nextInvoiceTotal: 19900,
    },
  })
  nextInvoice: {
    nextInvoiceDate: string // ISO 8601 string
    nextInvoiceTotal: number // amount in cents
  }

  @ApiProperty({
    description: 'Human-readable summary of the plan change',
    example:
      'Switching from Basic ($99/mo) to Pro ($199/mo). You will be charged $75.00 today for overages and proration.',
  })
  summaryMessage: string
}
