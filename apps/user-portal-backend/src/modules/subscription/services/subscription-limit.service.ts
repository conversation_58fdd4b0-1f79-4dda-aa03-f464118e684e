import {
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
  Inject,
} from '@nestjs/common'
import {
  LimitationType,
  ResourceType,
  PLAN_RESOURCE_TYPE_MAPPING,
  OveragePolicyType,
  SubscriptionStatus,
  PlanType,
} from '@libs/shared/constants/subscription'

import { Id } from '@backend/cores/base/id.type'
import SubscriptionRepository, {
  SUBSCRIPTION_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription.repository'
import SubscriptionPlanRepository, {
  SUBSCRIPTION_PLAN_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription-plan.repository'
import SubscriptionItemRepository, {
  SUBSCRIPTION_ITEM_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription-item.repository'
import SubscriptionUsageRepository, {
  SUBSCRIPTION_USAGE_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription-usage.repository'

@Injectable()
export class SubscriptionLimitService {
  private readonly logger = new Logger(SubscriptionLimitService.name)

  constructor(
    @Inject(SUBSCRIPTION_REPOSITORY)
    private readonly subscriptionRepository: SubscriptionRepository,
    @Inject(SUBSCRIPTION_PLAN_REPOSITORY)
    private readonly subscriptionPlanRepository: SubscriptionPlanRepository,
    @Inject(SUBSCRIPTION_ITEM_REPOSITORY)
    private readonly subscriptionItemRepository: SubscriptionItemRepository,
    @Inject(SUBSCRIPTION_USAGE_REPOSITORY)
    private readonly subscriptionUsageRepository: SubscriptionUsageRepository,
  ) {}

  async checkLimit(
    organizationId: Id,
    limitationType: LimitationType,
    currentOperationCount = 1,
  ): Promise<void> {
    this.logger.log(
      `Checking subscription limit for organization ${organizationId}, type ${limitationType}, count ${currentOperationCount}`,
    )

    // Check if this is a plan-limited resource type
    const resourceType = PLAN_RESOURCE_TYPE_MAPPING[limitationType]
    if (!resourceType) {
      // For non-plan-limited resources (like SMS, PHONE_CALLS), allow operation
      // These are handled via transactional billing, not plan limits
      this.logger.log(
        `Limitation type ${limitationType} is not plan-limited. Allowing operation.`,
      )
      return
    }

    // Get active subscription and plan with resource limits
    const { subscription, subscriptionPlan } =
      await this.getActiveSubscriptionPlan(organizationId)

    // Get current usage for this resource type
    const currentUsage = await this.getCurrentUsageForResource(
      organizationId,
      resourceType,
    )

    // Use domain method to check if operation would exceed limit
    const resourceLimit = subscriptionPlan.getResourceLimitFor(resourceType)
    if (!resourceLimit) {
      // SECURE DEFAULT: Missing limit configuration = 0 limit with BLOCK policy
      const errorMessage = `No resource limit configuration found for ${limitationType}. Resource access blocked for security.`
      this.logger.error(
        `CRITICAL: Missing resource limit configuration for organization ${organizationId}, resource type ${resourceType}`,
      )
      throw new HttpException(errorMessage, HttpStatus.FORBIDDEN)
    }

    const includedQuantity = resourceLimit.getProps().includedQuantity
    const newTotalUsage = currentUsage + currentOperationCount

    this.logger.log(
      `Current usage: ${currentUsage}, Plan limit: ${includedQuantity}, Operation count: ${currentOperationCount}, New total: ${newTotalUsage}`,
    )

    // Check if operation would exceed limit
    if (newTotalUsage > includedQuantity) {
      // Get overage policy from subscription
      const subscriptionProps = subscription.getProps()
      const overagePolicy =
        subscriptionProps.overagePolicyFixedResources || OveragePolicyType.BLOCK

      this.logger.log(
        `Limit would be exceeded. Checking overage policy: ${overagePolicy}`,
      )

      // Handle based on overage policy using domain logic
      switch (overagePolicy) {
        case OveragePolicyType.CHARGE:
          // Allow the operation - overage will be billed
          this.logger.log(
            `Overage policy is CHARGE. Allowing operation that will result in overage.`,
          )
          return

        case OveragePolicyType.GRACE:
          // Allow the operation - grace period logic (TBD per PRD)
          this.logger.log(
            `Overage policy is GRACE. Allowing operation within grace period.`,
          )
          return

        case OveragePolicyType.BLOCK:
        default: {
          // Block the operation
          const errorMessage = `Subscription limit exceeded for ${limitationType.toLowerCase()}. Current usage: ${currentUsage}/${includedQuantity}, Requested: ${currentOperationCount}`
          this.logger.warn(errorMessage)
          throw new HttpException(errorMessage, HttpStatus.FORBIDDEN)
        }
      }
    }

    this.logger.log(
      `Limit check passed for ${limitationType}: ${newTotalUsage}/${includedQuantity}`,
    )
  }

  /**
   * Get active subscription and plan with resource limits for an organization
   */
  private async getActiveSubscriptionPlan(organizationId: Id) {
    // Get active subscription
    const subscription = await this.subscriptionRepository.findOne({
      filter: {
        organizationId,
        status: SubscriptionStatus.ACTIVE,
      },
    })

    if (!subscription) {
      const errorMessage = `No active subscription found for organization ${organizationId}`
      this.logger.warn(errorMessage)
      throw new HttpException(errorMessage, HttpStatus.FORBIDDEN)
    }

    // Get active base plan item
    const subscriptionItemsResult = await this.subscriptionItemRepository.find({
      filter: {
        subscriptionId: subscription.id,
        status: SubscriptionStatus.ACTIVE,
      },
    })
    const subscriptionItems = subscriptionItemsResult.data

    const basePlanItem = subscriptionItems.find(
      (item) => item.getProps().itemType === PlanType.BASE_PLAN,
    )

    if (!basePlanItem) {
      const errorMessage = `No active base plan found for organization ${organizationId}`
      this.logger.warn(errorMessage)
      throw new HttpException(errorMessage, HttpStatus.FORBIDDEN)
    }

    // Get subscription plan with resource limits
    const subscriptionPlan =
      await this.subscriptionPlanRepository.findByIdWithLimits(
        basePlanItem.getProps().subscriptionPlanId,
      )

    if (!subscriptionPlan) {
      const errorMessage = `Subscription plan not found for organization ${organizationId}`
      this.logger.warn(errorMessage)
      throw new HttpException(errorMessage, HttpStatus.FORBIDDEN)
    }

    return { subscription, subscriptionPlan }
  }

  /**
   * Get current usage for a specific resource type dynamically
   */
  private async getCurrentUsageForResource(
    organizationId: Id,
    resourceType: ResourceType,
  ): Promise<number> {
    try {
      const usage =
        await this.subscriptionUsageRepository.getCurrentUsageByOrganizationId(
          organizationId,
        )

      // Dynamic mapping based on resource type
      switch (resourceType) {
        case ResourceType.MEMBER:
          return usage.totalUsers || 0
        case ResourceType.TEAM:
          return usage.totalTeams || 0
        case ResourceType.CHECK:
          return usage.totalChecks || 0
        case ResourceType.INTEGRATION:
          return usage.totalIntegrations || 0
        case ResourceType.STATUS_PAGE:
          return usage.totalStatusPages || 0
        default:
          this.logger.warn(`Unknown resource type: ${resourceType}`)
          return 0
      }
    } catch (error) {
      this.logger.error(
        `Failed to get current usage for organization ${organizationId}, resource ${resourceType}: ${error.message}`,
        error.stack,
      )
      // Return 0 on error to prevent blocking operations
      return 0
    }
  }

  /**
   * Check if an operation would be allowed without throwing exceptions
   * Useful for UI state management and pre-validation
   */
  async wouldOperationBeAllowed(
    organizationId: Id,
    limitationType: LimitationType,
    operationCount = 1,
  ): Promise<{
    allowed: boolean
    reason?: string
    currentUsage?: number
    limit?: number
  }> {
    try {
      // Check if this is a plan-limited resource type
      const resourceType = PLAN_RESOURCE_TYPE_MAPPING[limitationType]
      if (!resourceType) {
        return { allowed: true, reason: 'Non-plan-limited resource type' }
      }

      // Get active subscription and plan
      const { subscriptionPlan } =
        await this.getActiveSubscriptionPlan(organizationId)

      // Get resource limit
      const resourceLimit = subscriptionPlan.getResourceLimitFor(resourceType)
      if (!resourceLimit) {
        return {
          allowed: false,
          reason: 'No resource limit configuration found',
          currentUsage: 0,
          limit: 0,
        }
      }

      // Get current usage
      const currentUsage = await this.getCurrentUsageForResource(
        organizationId,
        resourceType,
      )
      const includedQuantity = resourceLimit.getProps().includedQuantity
      const newTotalUsage = currentUsage + operationCount

      if (newTotalUsage <= includedQuantity) {
        return {
          allowed: true,
          currentUsage,
          limit: includedQuantity,
        }
      }

      // Would exceed limit - check overage policy
      const subscription = await this.subscriptionRepository.findOne({
        filter: { organizationId, status: SubscriptionStatus.ACTIVE },
      })

      const overagePolicy =
        subscription?.getProps().overagePolicyFixedResources ||
        OveragePolicyType.BLOCK

      if (overagePolicy === OveragePolicyType.BLOCK) {
        return {
          allowed: false,
          reason: `Would exceed limit with BLOCK policy`,
          currentUsage,
          limit: includedQuantity,
        }
      }

      return {
        allowed: true,
        reason: `Overage allowed due to ${overagePolicy} policy`,
        currentUsage,
        limit: includedQuantity,
      }
    } catch (error) {
      this.logger.error(
        `Error checking if operation would be allowed: ${error.message}`,
        error.stack,
      )
      return {
        allowed: false,
        reason: `Error checking limits: ${error.message}`,
      }
    }
  }
}
