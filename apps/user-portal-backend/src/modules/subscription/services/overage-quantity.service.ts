import { Injectable, Logger, Inject } from '@nestjs/common'
import {
  OveragePolicyType,
  PlanType,
} from '@libs/shared/constants/subscription'
import { ResourceType } from '@libs/shared/constants/subscription/resource-mapping'

import SubscriptionRepository, {
  SUBSCRIPTION_REPOSITORY,
} from '../applications/subscription.repository'
import SubscriptionUsageRepository, {
  SUBSCRIPTION_USAGE_REPOSITORY,
} from '../applications/subscription-usage.repository'
import SubscriptionItemRepository, {
  SUBSCRIPTION_ITEM_REPOSITORY,
} from '../applications/subscription-item.repository'
import PlanResourceLimitRepository, {
  PLAN_RESOURCE_LIMIT_REPOSITORY,
} from '../applications/plan-resource-limit.repository'
import { StripeUseCase } from '../usecases/stripe.usecase'

export interface OverageQuantityMap {
  [resourceType: string]: number
}

export interface OverageUpdateResult {
  success: boolean
  updatedQuantities: OverageQuantityMap
  errors: Array<{ resourceType: string; error: string }>
}

export interface ResourceLimitInfo {
  resourceType: string
  limit: number
  overageStripePriceId: string
}

@Injectable()
export class OverageQuantityService {
  private readonly logger = new Logger(OverageQuantityService.name)

  constructor(
    @Inject(SUBSCRIPTION_REPOSITORY)
    private readonly subscriptionRepository: SubscriptionRepository,
    @Inject(SUBSCRIPTION_USAGE_REPOSITORY)
    private readonly subscriptionUsageRepository: SubscriptionUsageRepository,
    @Inject(SUBSCRIPTION_ITEM_REPOSITORY)
    private readonly subscriptionItemRepository: SubscriptionItemRepository,
    @Inject(PLAN_RESOURCE_LIMIT_REPOSITORY)
    private readonly planResourceLimitRepository: PlanResourceLimitRepository,
    private readonly stripeUseCase: StripeUseCase,
  ) {}

  /**
   * Calculates current overage quantities for all resource types
   */
  async calculateCurrentOverageQuantities(
    organizationId: string,
    planResourceLimits: ResourceLimitInfo[],
  ): Promise<OverageQuantityMap> {
    this.logger.log(
      `Calculating overage quantities for organization ${organizationId}`,
    )

    // Initialize dynamic overage quantities map
    const overageQuantities: OverageQuantityMap = {}

    // Calculate overage for each resource type dynamically
    for (const resourceLimit of planResourceLimits) {
      const currentCount = await this.getCurrentResourceUsage(
        organizationId,
        resourceLimit.resourceType,
      )
      const overageQuantity = Math.max(0, currentCount - resourceLimit.limit)

      overageQuantities[resourceLimit.resourceType] = overageQuantity

      this.logger.log(
        `Resource ${resourceLimit.resourceType}: current=${currentCount}, limit=${resourceLimit.limit}, overage=${overageQuantity}`,
      )
    }

    return overageQuantities
  }

  /**
   * Updates overage quantities for an organization's subscription
   */
  async updateOverageQuantitiesForOrganization(
    organizationId: string,
    subscriptionId: string,
    overagePolicy: OveragePolicyType,
  ): Promise<OverageUpdateResult> {
    this.logger.log(
      `Updating overage quantities for organization ${organizationId} with policy ${overagePolicy}`,
    )

    const result: OverageUpdateResult = {
      success: false,
      updatedQuantities: {},
      errors: [],
    }

    try {
      // Skip update for BLOCK policy (handled at API level)
      if (overagePolicy === OveragePolicyType.BLOCK) {
        this.logger.log('BLOCK policy - no Stripe quantity update needed')
        result.success = true
        return result
      }

      // Get subscription and plan details
      const subscription =
        await this.subscriptionRepository.findById(subscriptionId)
      if (!subscription) {
        throw new Error(`Subscription ${subscriptionId} not found`)
      }

      const subscriptionProps = subscription.getProps()
      const stripeSubscriptionId = subscriptionProps.stripeSubscriptionId

      // Get plan resource limits
      const planResourceLimits =
        await this.getPlanResourceLimits(subscriptionId)
      if (planResourceLimits.length === 0) {
        this.logger.log('No resource limits found for subscription')
        result.success = true
        return result
      }

      // Calculate current overage quantities
      const overageQuantities = await this.calculateCurrentOverageQuantities(
        organizationId,
        planResourceLimits,
      )

      // Determine proration behavior based on policy
      const prorationBehavior = this.mapOveragePolicyToProration(overagePolicy)

      // Update each resource type's subscription item quantity dynamically
      for (const resourceLimit of planResourceLimits) {
        const resourceType = resourceLimit.resourceType
        const newQuantity = overageQuantities[resourceType] || 0

        try {
          // Find the subscription item by subscription and resource type
          const subscriptionItem =
            await this.subscriptionItemRepository.findBySubscriptionAndResourceType(
              subscriptionId,
              resourceType as ResourceType,
            )

          if (subscriptionItem) {
            // Update existing subscription item quantity in Stripe
            const subscriptionItemProps = subscriptionItem.getProps()
            await this.stripeUseCase.updateSubscriptionItemQuantity(
              subscriptionItemProps.stripeSubscriptionItemId,
              newQuantity,
              prorationBehavior,
            )

            result.updatedQuantities[resourceType] = newQuantity

            this.logger.log(
              `Updated ${resourceType} overage quantity to ${newQuantity} for subscription ${stripeSubscriptionId}`,
            )
          } else {
            this.logger.warn(
              `No subscription item found for ${resourceType} in subscription ${subscriptionId}`,
            )
          }
        } catch (error) {
          const errorMessage = `Failed to update ${resourceType} overage: ${error.message}`
          this.logger.error(errorMessage, error.stack)
          result.errors.push({
            resourceType,
            error: errorMessage,
          })
        }
      }

      result.success = result.errors.length === 0

      this.logger.log(
        `Overage quantity update completed for organization ${organizationId}. Success: ${result.success}, Errors: ${result.errors.length}`,
      )

      return result
    } catch (error) {
      this.logger.error(
        `Failed to update overage quantities for organization ${organizationId}: ${error.message}`,
        error.stack,
      )

      result.errors.push({
        resourceType: 'UNKNOWN', // Generic error
        error: error.message,
      })

      return result
    }
  }

  /**
   * Gets current resource usage for an organization
   */
  private async getCurrentResourceUsage(
    organizationId: string,
    resourceType: string,
  ): Promise<number> {
    try {
      const usage =
        await this.subscriptionUsageRepository.getCurrentUsageByOrganizationId(
          organizationId,
        )

      // Map resource types to usage properties dynamically
      const usageMap: { [key: string]: number } = {
        MEMBER: usage.totalUsers || 0,
        TEAM: usage.totalTeams || 0,
        CHECK: usage.totalChecks || 0,
        INTEGRATION: usage.totalIntegrations || 0,
        STATUS_PAGE: usage.totalStatusPages || 0,
      }

      return usageMap[resourceType] || 0
    } catch (error) {
      this.logger.error(
        `Failed to get current usage for organization ${organizationId}: ${error.message}`,
        error.stack,
      )
      return 0
    }
  }

  /**
   * Gets plan resource limits for a subscription using PlanResourceLimitRepository
   */
  private async getPlanResourceLimits(
    subscriptionId: string,
  ): Promise<ResourceLimitInfo[]> {
    try {
      // Get base plan subscription item
      const subscriptionItemsResult =
        await this.subscriptionItemRepository.find({
          filter: { subscriptionId, itemType: PlanType.BASE_PLAN },
        })
      const subscriptionItems = subscriptionItemsResult.data

      if (subscriptionItems.length === 0) {
        this.logger.warn(
          `No base plan found for subscription ${subscriptionId}`,
        )
        return []
      }

      const basePlanItem = subscriptionItems[0]
      if (!basePlanItem) {
        this.logger.warn(
          `No base plan item found for subscription ${subscriptionId}`,
        )
        return []
      }

      const subscriptionPlanId = basePlanItem.getProps().subscriptionPlanId

      // Get plan resource limits using repository
      const planResourceLimits = await this.planResourceLimitRepository.findAll(
        {
          filter: { subscriptionPlanId },
        },
      )

      // Convert to ResourceLimitInfo format
      return planResourceLimits.map((limit) => {
        const props = limit.getProps()
        return {
          resourceType: props.resourceType,
          limit: props.includedQuantity,
          overageStripePriceId: props.overageStripePriceId,
        }
      })
    } catch (error) {
      this.logger.error(
        `Failed to get plan resource limits for subscription ${subscriptionId}: ${error.message}`,
        error.stack,
      )
      return []
    }
  }

  /**
   * Maps overage policy to Stripe proration behavior
   */
  private mapOveragePolicyToProration(
    overagePolicy: OveragePolicyType,
  ): 'create_prorations' | 'none' {
    switch (overagePolicy) {
      case OveragePolicyType.CHARGE:
        return 'create_prorations' // Immediate billing with proration
      case OveragePolicyType.GRACE:
        return 'none' // Bill at next cycle
      case OveragePolicyType.BLOCK:
        return 'none' // Should not reach here, but safe default
      default:
        this.logger.warn(
          `Unknown overage policy: ${overagePolicy}, defaulting to 'none'`,
        )
        return 'none'
    }
  }
}
