import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common'
import { SubscriptionStatus } from '@libs/shared/constants/subscription'

import { configService } from '@backend/cores/config/config.service'
import { Id } from '@backend/cores/base/id.type'
import SubscriptionRepository, {
  SUBSCRIPTION_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription.repository'
import SubscriptionPlanRepository, {
  SUBSCRIPTION_PLAN_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription-plan.repository'
import UserRepository, {
  USER_REPOSITORY,
} from '@backend/modules/user/applications/users.repository'
import OrganizationRepository, {
  ORGANIZATION_REPOSITORY,
} from '@backend/modules/user/applications/organization.repository'
import { StripeIntegrationHelper } from '@backend/modules/subscription/services/stripe-integration-helper.service'

/**
 * SubscriptionCheckoutUseCase handles the purchase flow for new subscriptions.
 * This includes Stripe customer creation, checkout session creation, and validation.
 */
@Injectable()
export class SubscriptionCheckoutUseCase {
  private readonly logger = new Logger(SubscriptionCheckoutUseCase.name)

  constructor(
    @Inject(SUBSCRIPTION_REPOSITORY)
    private readonly subscriptionRepository: SubscriptionRepository,
    @Inject(SUBSCRIPTION_PLAN_REPOSITORY)
    private readonly subscriptionPlanRepository: SubscriptionPlanRepository,
    @Inject(USER_REPOSITORY)
    private readonly userRepository: UserRepository,
    @Inject(ORGANIZATION_REPOSITORY)
    private readonly organizationRepository: OrganizationRepository,
    private readonly stripeIntegrationHelper: StripeIntegrationHelper,
  ) {}

  /**
   * Creates a Stripe Checkout Session for a subscription plan (PRD FR2.2.1).
   * @param userId - The internal ID of the user initiating the subscription.
   * @param planId - The SubscriptionPlan ID to subscribe to.
   * @param organizationId - The ID of the organization for which the subscription is being created.
   * @returns The Stripe Checkout Session object, specifically the URL for redirection.
   */
  async createCheckoutSession(
    userId: Id,
    planId: string,
    organizationId: string,
  ): Promise<{ sessionId: string; url: string | null }> {
    this.logger.log(
      `[SubscriptionCheckoutUseCase] Creating checkout session for user ${userId}, plan ${planId}`,
    )

    // 1. Validate user and organization ownership
    await this.validateUserOrganizationOwnership(userId, organizationId)

    // 2. Check for existing active subscription
    await this.validateNoActiveSubscription(organizationId)

    // 3. Validate user and get user details
    const user = await this.validateAndGetUser(userId)
    const userProps = user.getProps()

    if (!userProps.email) {
      this.logger.error(
        `User ${userId} is missing an email address, cannot create Stripe customer.`,
      )
      throw new HttpException(
        'User email is required for subscription.',
        HttpStatus.BAD_REQUEST,
      )
    }

    // 4. Fetch and validate subscription plan
    const subscriptionPlan = await this.validateAndGetSubscriptionPlan(planId)
    const planProps = subscriptionPlan.getProps()

    if (!planProps.stripePriceId) {
      this.logger.error(`SubscriptionPlan ${planId} has no Stripe price ID.`)
      throw new HttpException(
        'Selected subscription plan is not properly configured.',
        HttpStatus.BAD_REQUEST,
      )
    }

    // 5. Get all resource limits for this plan
    const resourceLimits = subscriptionPlan.getAllResourceLimits()
    this.logger.log(
      `Found ${resourceLimits.length} resource limits for plan ${planId}`,
    )

    // 6. Ensure Stripe customer exists
    let stripeCustomerId = userProps.stripeCustomerId
    if (!stripeCustomerId) {
      stripeCustomerId =
        await this.stripeIntegrationHelper.ensureStripeCustomer(
          userId,
          userProps.email,
          `${userProps.firstName} ${userProps.lastName}`,
          async (customerId: string) => {
            if (customerId !== userProps.stripeCustomerId) {
              await this.userRepository.updateById(userId, {
                ...userProps,
                stripeCustomerId: customerId,
              })
              this.logger.log(
                `Updated user ${userId} with Stripe customer ID ${customerId}`,
              )
            }
          },
        )
    }

    // 7. Build checkout metadata with dynamic overage price IDs
    const { metadata, validOveragePrices, invalidLimits } =
      this.stripeIntegrationHelper.buildCheckoutMetadata(
        planId,
        organizationId,
        resourceLimits,
      )

    // 8. Validate overage configuration
    if (invalidLimits.length > 0) {
      this.logger.warn(
        `Plan ${planId} has invalid resource limits for: ${invalidLimits.join(', ')}. Continuing with valid limits only.`,
      )
    }

    this.logger.log(
      `Creating checkout session with ${validOveragePrices.length} valid overage prices for plan ${planId}: ${validOveragePrices.map((p) => `${p.resourceType}=${p.priceId}`).join(', ')}`,
    )

    // 9. Get config URLs
    const successUrl = configService.getValue('STRIPE_CHECKOUT_SUCCESS_URL')
    const cancelUrl = configService.getValue('STRIPE_CHECKOUT_CANCEL_URL')

    // 10. Create Stripe Checkout Session
    try {
      const session =
        await this.stripeIntegrationHelper.createSubscriptionCheckoutSession(
          stripeCustomerId,
          userId.toString(),
          planProps.stripePriceId, // Base plan's recurring price
          metadata,
          successUrl,
          cancelUrl,
        )

      this.logger.log(
        `Successfully created checkout session ${session.sessionId} for user ${userId} with plan ${planId}`,
      )

      return session
    } catch (error) {
      this.logger.error(
        `Failed to create Stripe checkout session for user ${userId}: ${error.message}`,
        error.stack,
      )
      throw new HttpException(
        'Failed to create subscription session.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  /**
   * Validates that the user is the owner of the organization
   */
  private async validateUserOrganizationOwnership(
    userId: Id,
    organizationId: string,
  ): Promise<void> {
    const organization =
      await this.organizationRepository.findById(organizationId)

    if (!organization || organization.getProps().ownerId !== userId) {
      this.logger.error(`User ${userId} is not an owner of Organization.`)
      throw new HttpException(
        `User is not an owner of Organization.`,
        HttpStatus.BAD_REQUEST,
      )
    }
  }

  /**
   * Validates that the organization doesn't have an active subscription
   */
  private async validateNoActiveSubscription(
    organizationId: string,
  ): Promise<void> {
    const existingSubscription = await this.subscriptionRepository.findOne({
      filter: {
        organizationId,
        status: SubscriptionStatus.ACTIVE,
      },
    })

    if (existingSubscription) {
      this.logger.error(
        `Organization ${organizationId} already has an active subscription ${existingSubscription.id}`,
      )
      throw new HttpException(
        'Organization already has an active subscription. Please manage your existing subscription instead.',
        HttpStatus.CONFLICT,
      )
    }
  }

  /**
   * Validates and retrieves user information
   */
  private async validateAndGetUser(userId: Id) {
    const user = await this.userRepository.findById(userId)

    if (!user) {
      this.logger.error(`User with ID ${userId} not found.`)
      throw new NotFoundException(`User with ID ${userId} not found.`)
    }

    return user
  }

  /**
   * Validates and retrieves subscription plan with resource limits
   */
  private async validateAndGetSubscriptionPlan(planId: string) {
    const subscriptionPlan =
      await this.subscriptionPlanRepository.findByIdWithLimits(planId)

    if (!subscriptionPlan) {
      this.logger.error(`SubscriptionPlan with ID ${planId} not found.`)
      throw new NotFoundException(
        `SubscriptionPlan with ID ${planId} not found.`,
      )
    }

    const planProps = subscriptionPlan.getProps()
    if (!planProps.isActive) {
      this.logger.error(`SubscriptionPlan ${planId} is not active.`)
      throw new HttpException(
        'Selected subscription plan is not available.',
        HttpStatus.BAD_REQUEST,
      )
    }

    return subscriptionPlan
  }
}
