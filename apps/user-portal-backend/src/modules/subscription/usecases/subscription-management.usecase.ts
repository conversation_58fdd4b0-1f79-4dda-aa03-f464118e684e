import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common'
import { SubscriptionStatus } from '@libs/shared/constants/subscription'

import { Id } from '@backend/cores/base/id.type'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { PaginateDto } from '@backend/commons/dto/paginate.dto'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'
import SubscriptionRepository, {
  SUBSCRIPTION_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription.repository'
import { CreateSubscriptionDto } from '@backend/modules/subscription/applications/dto/subscription.request.dto'
import { SubscriptionResponseDto } from '@backend/modules/subscription/applications/dto/subscription.response.dto'

/**
 * SubscriptionManagementUseCase handles basic CRUD operations for subscriptions.
 * This is a focused use case that only deals with simple subscription management
 * without complex business logic like billing, plan changes, or Stripe integration.
 */
@Injectable()
export class SubscriptionManagementUseCase {
  private readonly logger = new Logger(SubscriptionManagementUseCase.name)

  constructor(
    @Inject(SUBSCRIPTION_REPOSITORY)
    private readonly subscriptionRepository: SubscriptionRepository,
  ) {}

  /**
   * Finds subscriptions with pagination for an organization
   */
  async find(
    paginateOption: PaginateOptionsDto,
    orgTeamParams: OrgTeamHeadersDto,
  ): Promise<PaginateDto<SubscriptionResponseDto>> {
    try {
      this.logger.log(
        `Finding subscriptions for organization ${orgTeamParams.organizationId}`,
      )

      const subscriptions = await this.subscriptionRepository.find({
        page: paginateOption.page,
        limit: paginateOption.limit,
        orderBy: {
          createdAt: 'DESC',
        },
        filter: { organizationId: orgTeamParams.organizationId },
      })

      this.logger.log(
        `Found ${subscriptions.data.length} subscriptions for organization ${orgTeamParams.organizationId}`,
      )

      return {
        data: subscriptions.data.map((sub) => sub.toResponse()),
        total: subscriptions.total,
        totalPage: subscriptions.totalPage,
        limit: subscriptions.limit,
        page: subscriptions.page,
      }
    } catch (error) {
      this.logger.error(
        `Failed to find subscriptions for organization ${orgTeamParams.organizationId}: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Finds a single subscription by ID
   */
  async findOne(id: Id): Promise<SubscriptionResponseDto | null> {
    try {
      this.logger.log(`Finding subscription by ID: ${id}`)

      const entity = await this.subscriptionRepository.findById(id)

      if (!entity) {
        this.logger.log(`Subscription not found: ${id}`)
        return null
      }

      this.logger.log(`Found subscription: ${id}`)
      return entity.toResponse()
    } catch (error) {
      this.logger.error(
        `Failed to find subscription ${id}: ${error.message}`,
        error.stack,
      )
      throw error
    }
  }

  /**
   * Updates a subscription with basic information
   * Note: This does not handle complex updates like plan changes or billing updates
   */
  async update(
    id: Id,
    updateSubscriptionDto: Partial<CreateSubscriptionDto>,
  ): Promise<SubscriptionResponseDto> {
    try {
      this.logger.log(`Updating subscription: ${id}`)

      const subscription = await this.subscriptionRepository.findById(id)

      if (!subscription) {
        throw new NotFoundException(`Subscription with ID ${id} not found.`)
      }

      // Perform the update
      const updatedSubscription = await this.subscriptionRepository.updateById(
        id,
        {
          ...subscription.getProps(),
          ...updateSubscriptionDto,
        },
      )

      if (!updatedSubscription) {
        throw new HttpException(
          'Failed to update subscription.',
          HttpStatus.INTERNAL_SERVER_ERROR,
        )
      }

      this.logger.log(`Successfully updated subscription: ${id}`)
      return updatedSubscription.toResponse()
    } catch (error) {
      this.logger.error(
        `Failed to update subscription ${id}: ${error.message}`,
        error.stack,
      )
      if (error instanceof HttpException) throw error
      throw new HttpException(
        error.message || 'Failed to update subscription',
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  /**
   * Finds all subscriptions for an organization
   */
  async findSubscriptionsByOrganizationId(
    organizationId: string,
  ): Promise<SubscriptionResponseDto[]> {
    try {
      this.logger.log(
        `Finding all subscriptions for organization: ${organizationId}`,
      )

      const subscriptionsResult = await this.subscriptionRepository.find({
        filter: {
          organizationId,
        },
        relations: {
          subscriptionItems: true,
        },
      })
      const subscriptions = subscriptionsResult.data

      this.logger.log(
        `Found ${subscriptions?.length || 0} subscriptions for organization: ${organizationId}`,
      )

      return (
        subscriptions?.map((subscription) => subscription.toResponse()) || []
      )
    } catch (error) {
      this.logger.error(
        `Failed to find subscriptions for organization ${organizationId}: ${error.message}`,
        error.stack,
      )
      throw new HttpException(
        error.message || 'Failed to find subscriptions',
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  /**
   * Finds the active subscription for an organization
   */
  async findActiveSubscriptionByOrganizationId(
    organizationId: string,
  ): Promise<SubscriptionResponseDto | null> {
    try {
      this.logger.log(
        `Finding active subscription for organization: ${organizationId}`,
      )

      const subscription = await this.subscriptionRepository.findOne({
        filter: {
          organizationId,
          status: SubscriptionStatus.ACTIVE,
        },
      })

      if (!subscription) {
        this.logger.log(
          `No active subscription found for organization: ${organizationId}`,
        )
        return null
      }

      this.logger.log(
        `Found active subscription ${subscription.id} for organization: ${organizationId}`,
      )
      return subscription.toResponse()
    } catch (error) {
      this.logger.error(
        `Failed to find active subscription for organization ${organizationId}: ${error.message}`,
        error.stack,
      )
      throw new HttpException(
        error.message || 'Failed to find active subscription',
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  /**
   * Checks if an organization has an active subscription
   */
  async hasActiveSubscription(organizationId: string): Promise<boolean> {
    try {
      const activeSubscription =
        await this.findActiveSubscriptionByOrganizationId(organizationId)
      return activeSubscription !== null
    } catch (error) {
      this.logger.error(
        `Failed to check active subscription for organization ${organizationId}: ${error.message}`,
        error.stack,
      )
      // Return false on error to be safe
      return false
    }
  }
}
