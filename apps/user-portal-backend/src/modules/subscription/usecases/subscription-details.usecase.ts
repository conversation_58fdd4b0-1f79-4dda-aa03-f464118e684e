import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
} from '@nestjs/common'
import {
  PlanType,
  ResourceType,
  SubscriptionStatus,
} from '@libs/shared/constants/subscription'

import { Id } from '@backend/cores/base/id.type'
import SubscriptionRepository, {
  SUBSCRIPTION_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription.repository'
import SubscriptionUsageRepository, {
  SUBSCRIPTION_USAGE_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription-usage.repository'
import SubscriptionPlanRepository, {
  SUBSCRIPTION_PLAN_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription-plan.repository'
import SubscriptionItemRepository, {
  SUBSCRIPTION_ITEM_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription-item.repository'
import { SubscriptionDetailResponseDto } from '@backend/modules/subscription/applications/dto/subscription-detail.response.dto'
import {
  SubscriptionItem,
  SubscriptionPlanResponse,
} from '@backend/modules/subscription/entities'
import { UsageCalculationService } from '@backend/modules/subscription/services/usage-calculation.service'

import { StripeUseCase } from './stripe.usecase'

/**
 * SubscriptionDetailsUseCase handles complex data aggregation for subscription details.
 * This includes usage tracking, billing calculations, and subscription overview data.
 */
@Injectable()
export class SubscriptionDetailsUseCase {
  private readonly logger = new Logger(SubscriptionDetailsUseCase.name)

  constructor(
    @Inject(SUBSCRIPTION_REPOSITORY)
    private readonly subscriptionRepository: SubscriptionRepository,
    @Inject(SUBSCRIPTION_ITEM_REPOSITORY)
    private readonly subscriptionItemRepository: SubscriptionItemRepository,
    @Inject(SUBSCRIPTION_PLAN_REPOSITORY)
    private readonly subscriptionPlanRepository: SubscriptionPlanRepository,
    @Inject(SUBSCRIPTION_USAGE_REPOSITORY)
    private readonly subscriptionUsageRepository: SubscriptionUsageRepository,
    private readonly usageCalculationService: UsageCalculationService,
    private readonly stripeUseCase: StripeUseCase,
  ) {}

  /**
   * Gets subscription details using the new relational structure.
   * @param organizationId - The organization's ID.
   */
  async getSubscriptionDetails(
    organizationId: Id,
  ): Promise<SubscriptionDetailResponseDto | null> {
    try {
      this.logger.log(
        `Getting subscription details for organization ${organizationId}`,
      )

      // 1. Get the active subscription
      const subscription = await this.subscriptionRepository.findOne({
        filter: {
          organizationId,
          status: SubscriptionStatus.ACTIVE,
        },
      })

      if (!subscription) {
        this.logger.log(
          `No active subscription found for organization ${organizationId}`,
        )
        return null
      }

      // 2. Get subscription items (now separate entities)
      const subscriptionItems = await this.subscriptionItemRepository.findAll({
        filter: {
          subscriptionId: subscription.id,
        },
      })

      // 3. Get the base plan details with Stripe price information
      const basePlanItem = subscriptionItems.find(
        (item) => item.getProps().itemType === PlanType.BASE_PLAN,
      )

      let planDetails: SubscriptionPlanResponse | null = null
      if (basePlanItem) {
        const currentPlan =
          await this.subscriptionPlanRepository.findByIdWithLimits(
            basePlanItem.getProps().subscriptionPlanId,
          )

        if (currentPlan) {
          // Collect all Stripe price IDs that need to be fetched
          const allPriceIds = new Set<string>()
          const props = currentPlan.getProps()
          if (props.stripePriceId) {
            allPriceIds.add(props.stripePriceId)
          }
          // Add overage price IDs from resource limits
          currentPlan.getAllResourceLimits().forEach((limit) => {
            const limitProps = limit.getProps()
            if (limitProps.overageStripePriceId) {
              allPriceIds.add(limitProps.overageStripePriceId)
            }
          })

          // Fetch all Stripe prices in one batch
          const stripePrices = await this.stripeUseCase.getStripePrices(
            Array.from(allPriceIds),
          )

          planDetails = currentPlan.toResponse()

          // Add base plan price information
          if (props.stripePriceId && stripePrices.has(props.stripePriceId)) {
            const stripePrice = stripePrices.get(props.stripePriceId)!
            planDetails.price = {
              id: stripePrice.id,
              amount: stripePrice.unit_amount,
              currency: stripePrice.currency,
              recurring: stripePrice.recurring,
            }
          }

          // Add price information to resource limits
          if (planDetails.resourceLimits) {
            planDetails.resourceLimits = planDetails.resourceLimits.map(
              (limit) => {
                if (
                  limit.overageStripePriceId &&
                  stripePrices.has(limit.overageStripePriceId)
                ) {
                  const stripePrice = stripePrices.get(
                    limit.overageStripePriceId,
                  )!
                  limit.overagePrice = {
                    id: stripePrice.id,
                    amount: stripePrice.unit_amount,
                    currency: stripePrice.currency,
                    recurring: stripePrice.recurring,
                  }
                }
                return limit
              },
            )
          }
        }
      }

      // 4. Get usage history from the new event log structure
      const recentUsageEvents = await this.subscriptionUsageRepository.findAll({
        filter: { organizationId },
        limit: 100,
        orderBy: { usageTimestamp: 'DESC' },
      })

      // 5. Calculate current usage totals from actual usage tracking
      const currentUsage =
        await this.getCurrentUsageByOrganization(organizationId)

      // 6. Calculate resource limits and overage status
      const { resourceLimits } = await this.calculateResourceLimitsAndOverages(
        basePlanItem!,
        currentUsage,
      )

      // 7. Get usage billing data for transactional resources (SMS, Phone calls)
      const subscriptionProps = subscription.getProps()
      const { currentPeriodUsage, previousPeriodUsage } =
        await this.calculateUsageBillingForPeriods(
          organizationId,
          subscriptionProps.currentPeriodStartDate,
          subscriptionProps.currentPeriodEndDate,
        )

      const response: SubscriptionDetailResponseDto = {
        ...subscription.toResponse(),
        subscriptionItems: subscriptionItems.map((item) => item.toResponse()),
        planDetails,
        usageHistory: recentUsageEvents.map((event) => event.toResponse()),
        resourceLimits,
        usageBilling: {
          currentPeriod: currentPeriodUsage,
          previousPeriod: previousPeriodUsage,
        },
      }

      this.logger.log(
        `Successfully retrieved subscription details for organization ${organizationId}`,
      )

      return response
    } catch (error) {
      this.logger.error(
        `Failed to get subscription details for Organization ${organizationId}: ${error.message}`,
        error.stack,
      )
      throw new HttpException(
        error.message || 'Failed to get subscription details',
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  /**
   * Gets usage history from the event log structure.
   * @param organizationId - The organization's ID.
   * @param filters - Optional filters for usage type, date range, etc.
   */
  async getUsageHistory(
    organizationId: string,
    filters?: {
      usageType?: string
      startDate?: Date
      endDate?: Date
      limit?: number
      offset?: number
    },
  ): Promise<{
    events: any[]
    total: number
    aggregatedUsage: Record<string, number>
  }> {
    try {
      this.logger.log(
        `Getting usage history for organization ${organizationId}`,
      )

      // Build filter for the event log query
      const filter: any = { organizationId }

      if (filters?.usageType) {
        filter.usageType = filters.usageType
      }

      if (filters?.startDate || filters?.endDate) {
        filter.usageTimestamp = {}
        if (filters.startDate) {
          filter.usageTimestamp.gte = filters.startDate
        }
        if (filters.endDate) {
          filter.usageTimestamp.lte = filters.endDate
        }
      }

      // Get usage events from the event log
      const usageEvents = await this.subscriptionUsageRepository.findAll({
        filter,
        limit: filters?.limit || 100,
        offset: filters?.offset || 0,
        orderBy: { usageTimestamp: 'DESC' },
      })

      // Get total count for pagination
      const totalCount = await this.subscriptionUsageRepository.count({
        filter,
      })

      // Aggregate usage using the service
      const aggregatedUsage =
        this.usageCalculationService.aggregateUsageEvents(usageEvents)

      this.logger.log(
        `Retrieved ${usageEvents.length} usage events for organization ${organizationId}`,
      )

      return {
        events: usageEvents.map((event) => event.toResponse()),
        total: totalCount,
        aggregatedUsage,
      }
    } catch (error) {
      this.logger.error(
        `Failed to get usage history for organization ${organizationId}: ${error.message}`,
        error.stack,
      )
      throw new HttpException(
        error.message || 'Failed to get usage history',
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  /**
   * Helper method to get current usage for an organization.
   */
  async getCurrentUsageByOrganization(organizationId: string): Promise<{
    members: number
    teams: number
    checks: number
  }> {
    try {
      this.logger.log(
        `Getting current usage for organization ${organizationId}`,
      )

      const usage =
        await this.subscriptionUsageRepository.getCurrentUsageByOrganizationId(
          organizationId,
        )

      return {
        members: usage.totalUsers,
        teams: usage.totalTeams,
        checks: usage.totalChecks,
      }
    } catch (error) {
      this.logger.error(
        `Failed to get current usage for organization ${organizationId}: ${error.message}`,
        error.stack,
      )
      // Return zero values on error to prevent blocking other operations
      return {
        members: 0,
        teams: 0,
        checks: 0,
      }
    }
  }

  /**
   * Calculates resource limits and overage status for a subscription plan
   */
  private async calculateResourceLimitsAndOverages(
    basePlanItem: SubscriptionItem,
    currentUsage: { members: number; teams: number; checks: number },
  ): Promise<{
    resourceLimits: Array<{
      resourceType: string
      includedQuantity: number
      currentUsage: number
      overage: number
      overagePrice?: number
      stripePriceId?: string
    }>
  }> {
    const resourceLimits: Array<{
      resourceType: string
      includedQuantity: number
      currentUsage: number
      overage: number
      overagePrice?: number
      stripePriceId?: string
    }> = []

    if (basePlanItem) {
      const currentPlan =
        await this.subscriptionPlanRepository.findByIdWithLimits(
          basePlanItem.getProps().subscriptionPlanId,
        )

      if (currentPlan) {
        const memberLimit = currentPlan.getResourceLimitFor(ResourceType.MEMBER)
        const teamLimit = currentPlan.getResourceLimitFor(ResourceType.TEAM)
        const checkLimit = currentPlan.getResourceLimitFor(ResourceType.CHECK)
        const integrationLimit = currentPlan.getResourceLimitFor(
          ResourceType.INTEGRATION,
        )
        const statusPageLimit = currentPlan.getResourceLimitFor(
          ResourceType.STATUS_PAGE,
        )

        // Build resource limits array
        const allLimits = [
          {
            type: ResourceType.MEMBER,
            limit: memberLimit,
            usage: currentUsage.members,
          },
          {
            type: ResourceType.TEAM,
            limit: teamLimit,
            usage: currentUsage.teams,
          },
          {
            type: ResourceType.CHECK,
            limit: checkLimit,
            usage: currentUsage.checks,
          },
          {
            type: ResourceType.INTEGRATION,
            limit: integrationLimit,
            usage: 0,
          }, // TODO: Get actual
          {
            type: ResourceType.STATUS_PAGE,
            limit: statusPageLimit,
            usage: 0,
          }, // TODO: Get actual
        ]

        for (const { type, limit, usage } of allLimits) {
          if (limit) {
            const limitProps = limit.getProps()
            resourceLimits.push({
              resourceType: type,
              includedQuantity: limitProps.includedQuantity,
              currentUsage: usage,
              overage: Math.max(0, usage - limitProps.includedQuantity),
              overagePrice: limitProps.overagePrice,
              stripePriceId: limitProps.overageStripePriceId,
            })
          }
        }
      }
    }

    return {
      resourceLimits,
    }
  }

  /**
   * Calculates usage billing for current and previous periods
   */
  private async calculateUsageBillingForPeriods(
    organizationId: string,
    currentPeriodStart: Date,
    currentPeriodEnd: Date,
  ): Promise<{
    currentPeriodUsage: any
    previousPeriodUsage: any
  }> {
    try {
      // Get current period usage billing
      const smsUsageCurrentPeriod =
        await this.subscriptionUsageRepository.getUsageByBillingPeriod(
          organizationId,
          currentPeriodStart,
          currentPeriodEnd,
          'SMS',
        )

      const phoneUsageCurrentPeriod =
        await this.subscriptionUsageRepository.getUsageByBillingPeriod(
          organizationId,
          currentPeriodStart,
          currentPeriodEnd,
          'PHONE_CALL',
        )

      const currentPeriodUsage =
        await this.usageCalculationService.calculateUsageBillingForPeriod(
          [...smsUsageCurrentPeriod, ...phoneUsageCurrentPeriod],
          currentPeriodStart,
          currentPeriodEnd,
        )

      // Get previous period usage billing (optional)
      let previousPeriodUsage: any = undefined
      const previousPeriodStart = new Date(currentPeriodStart)
      previousPeriodStart.setMonth(previousPeriodStart.getMonth() - 1)
      const previousPeriodEnd = new Date(currentPeriodEnd)
      previousPeriodEnd.setMonth(previousPeriodEnd.getMonth() - 1)

      try {
        const smsUsagePreviousPeriod =
          await this.subscriptionUsageRepository.getUsageByBillingPeriod(
            organizationId,
            previousPeriodStart,
            previousPeriodEnd,
            'SMS',
          )

        const phoneUsagePreviousPeriod =
          await this.subscriptionUsageRepository.getUsageByBillingPeriod(
            organizationId,
            previousPeriodStart,
            previousPeriodEnd,
            'PHONE_CALL',
          )

        previousPeriodUsage =
          await this.usageCalculationService.calculateUsageBillingForPeriod(
            [...smsUsagePreviousPeriod, ...phoneUsagePreviousPeriod],
            previousPeriodStart,
            previousPeriodEnd,
          )
      } catch (error) {
        this.logger.warn(
          `Failed to get previous period usage billing: ${error.message}`,
        )
      }

      return {
        currentPeriodUsage,
        previousPeriodUsage,
      }
    } catch (error) {
      this.logger.error(
        `Failed to calculate usage billing for organization ${organizationId}: ${error.message}`,
        error.stack,
      )
      // Return empty objects on error
      return {
        currentPeriodUsage: {
          startDate: currentPeriodStart,
          endDate: currentPeriodEnd,
          sms: { count: 0, cost: 0 },
          phoneCalls: { count: 0, cost: 0 },
          totalCost: 0,
        },
        previousPeriodUsage: undefined,
      }
    }
  }
}
