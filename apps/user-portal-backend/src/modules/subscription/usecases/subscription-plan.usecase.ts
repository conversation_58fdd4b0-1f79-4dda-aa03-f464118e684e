/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common'
import { ResourceType } from '@libs/shared/constants/subscription'
import { UsageTypeUtils } from '@libs/shared/utils/usage-type.utils'

import { Id } from '@backend/cores/base/id.type'
import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { PaginateDto } from '@backend/commons/dto/paginate.dto'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import SubscriptionPlanRepository, {
  SUBSCRIPTION_PLAN_REPOSITORY,
} from '@backend/modules/subscription/applications/subscription-plan.repository'
import {
  CreateSubscriptionPlanDto,
  UpdateSubscriptionPlanDto,
} from '@backend/modules/subscription/applications/dto/subscription-plan.request.dto'
import { SubscriptionPlanResponseDto } from '@backend/modules/subscription/applications/dto/subscription-plan.response.dto'
import { SubscriptionPlan } from '@backend/modules/subscription/entities'
import { PlanResourceLimitProps } from '@backend/modules/subscription/entities/plan-resource-limit.entity'

import { Period, PlanSource } from '../types/subscription-plan.types'

import { StripeUseCase } from './stripe.usecase'

// Keep amounts in cents for consistency - no conversion needed

@Injectable()
export class SubscriptionPlanUseCase {
  constructor(
    @Inject(SUBSCRIPTION_PLAN_REPOSITORY)
    private readonly repo: SubscriptionPlanRepository,
    private readonly stripeUseCase: StripeUseCase,
  ) {}

  async create(
    createSubscriptionPlanDto: CreateSubscriptionPlanDto,
    user: JwtPayload,
  ): Promise<SubscriptionPlanResponseDto> {
    try {
      const { resourceLimits, price, ...planProps } = createSubscriptionPlanDto
      let basePriceId: string | undefined
      if (createSubscriptionPlanDto.source === PlanSource.STRIPE) {
        // For dynamic pricing plans, create base price - price is already in cents
        const basePrice = await this.stripeUseCase.createPrice({
          unit_amount: price, // Price already in cents
          currency: 'usd',
          recurring: {
            interval:
              createSubscriptionPlanDto.period === Period.YEAR
                ? 'year'
                : 'month',
          },
          product_data: {
            name: `${createSubscriptionPlanDto.name}`,
            metadata: {
              planName: createSubscriptionPlanDto.name,
              priceType: 'base',
              isDynamicPricing: 'true',
            },
          },
        })
        basePriceId = basePrice.id
      }

      const planData = {
        ...planProps,
        stripePriceId: basePriceId,
        createdBy: user.userId,
        updatedBy: user.userId,
      }

      // Create plan entity
      const planEntity = SubscriptionPlan.create({
        ...planData,
        createdBy: user.userId,
        updatedBy: user.userId,
      })

      let newSubscriptionPlan: SubscriptionPlan | null

      if (resourceLimits && resourceLimits.length > 0) {
        const limitsData: Omit<PlanResourceLimitProps, 'subscriptionPlanId'>[] =
          []
        for (const limit of resourceLimits) {
          const overagePrice = await this.stripeUseCase.createPrice({
            unit_amount: limit.overagePrice, // Price already in cents
            currency: 'usd',
            recurring: {
              interval:
                createSubscriptionPlanDto.period === Period.YEAR
                  ? 'year'
                  : 'month',
              usage_type: 'metered',
            },
            product_data: {
              name: `${createSubscriptionPlanDto.name} - ${limit.resourceType} Overage`,
              metadata: {
                planName: createSubscriptionPlanDto.name,
                priceType: UsageTypeUtils.generateOverageUsageType(
                  limit.resourceType as ResourceType,
                ),
              },
            },
          })

          limitsData.push({
            ...limit,
            overageStripePriceId: overagePrice.id,
            createdBy: user.userId,
            updatedBy: user.userId,
          })
        }

        newSubscriptionPlan = await this.repo.createWithLimits(
          planEntity,
          limitsData,
        )
      } else {
        // Create plan without resource limits
        newSubscriptionPlan = await this.repo.create(planEntity)
      }

      if (!newSubscriptionPlan) {
        throw new Error('Failed to create subscription plan')
      }

      return newSubscriptionPlan.toResponse()
    } catch (error) {
      console.error(error)
      throw new HttpException(
        error instanceof Error
          ? error.message
          : 'Failed to create subscription plan',
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  async find(
    paginateOption: PaginateOptionsDto,
  ): Promise<PaginateDto<SubscriptionPlanResponseDto>> {
    try {
      const plans = await this.repo.find({
        page: paginateOption.page,
        limit: paginateOption.limit,
        orderBy: {
          createdAt: 'DESC',
        },
      })

      // Load resource limits for each plan
      const plansWithLimits = await Promise.all(
        plans.data.map(async (plan) => {
          const planWithLimits = await this.repo.findByIdWithLimits(plan.id)
          return planWithLimits || plan
        }),
      )

      // Collect all Stripe price IDs that need to be fetched
      const allPriceIds = new Set<string>()
      plansWithLimits.forEach((plan) => {
        const props = plan.getProps()
        if (props.stripePriceId) {
          allPriceIds.add(props.stripePriceId)
        }
        // Add overage price IDs from resource limits
        plan.getAllResourceLimits().forEach((limit) => {
          const limitProps = limit.getProps()
          if (limitProps.overageStripePriceId) {
            allPriceIds.add(limitProps.overageStripePriceId)
          }
        })
      })

      // Fetch all Stripe prices in one batch
      const stripePrices = await this.stripeUseCase.getStripePrices(
        Array.from(allPriceIds),
      )

      return {
        data: plansWithLimits.map((plan) => {
          const response = plan.toResponse()
          const props = plan.getProps()

          // Add base plan price information
          if (props.stripePriceId && stripePrices.has(props.stripePriceId)) {
            const stripePrice = stripePrices.get(props.stripePriceId)!
            response.price = {
              id: stripePrice.id,
              amount: stripePrice.unit_amount,
              currency: stripePrice.currency,
              recurring: stripePrice.recurring,
            }
          }

          // Add price information to resource limits
          if (response.resourceLimits) {
            response.resourceLimits = response.resourceLimits.map((limit) => {
              if (
                limit.overageStripePriceId &&
                stripePrices.has(limit.overageStripePriceId)
              ) {
                const stripePrice = stripePrices.get(
                  limit.overageStripePriceId,
                )!
                limit.overagePrice = {
                  id: stripePrice.id,
                  amount: stripePrice.unit_amount,
                  currency: stripePrice.currency,
                  recurring: stripePrice.recurring,
                }
              }
              return limit
            })
          }

          return response
        }),
        total: plans.total,
        totalPage: plans.totalPage,
        limit: plans.limit,
        page: plans.page,
      }
    } catch (error) {
      console.error(error)
      throw error
    }
  }

  async findOne(id: Id): Promise<Nullable<SubscriptionPlanResponseDto>> {
    const entity = await this.repo.findByIdWithLimits(id)

    if (!entity) return null

    // Collect all Stripe price IDs that need to be fetched
    const allPriceIds = new Set<string>()
    const props = entity.getProps()
    if (props.stripePriceId) {
      allPriceIds.add(props.stripePriceId)
    }
    // Add overage price IDs from resource limits
    entity.getAllResourceLimits().forEach((limit) => {
      const limitProps = limit.getProps()
      if (limitProps.overageStripePriceId) {
        allPriceIds.add(limitProps.overageStripePriceId)
      }
    })

    // Fetch all Stripe prices in one batch
    const stripePrices = await this.stripeUseCase.getStripePrices(
      Array.from(allPriceIds),
    )

    const response = entity.toResponse()

    // Add base plan price information
    if (props.stripePriceId && stripePrices.has(props.stripePriceId)) {
      const stripePrice = stripePrices.get(props.stripePriceId)!
      response.price = {
        id: stripePrice.id,
        amount: stripePrice.unit_amount,
        currency: stripePrice.currency,
        recurring: stripePrice.recurring,
      }
    }

    // Add price information to resource limits
    if (response.resourceLimits) {
      response.resourceLimits = response.resourceLimits.map((limit) => {
        if (
          limit.overageStripePriceId &&
          stripePrices.has(limit.overageStripePriceId)
        ) {
          const stripePrice = stripePrices.get(limit.overageStripePriceId)!
          limit.overagePrice = {
            id: stripePrice.id,
            amount: stripePrice.unit_amount,
            currency: stripePrice.currency,
            recurring: stripePrice.recurring,
          }
        }
        return limit
      })
    }

    return response
  }

  async update(
    id: Id,
    updateSubscriptionPlanDto: UpdateSubscriptionPlanDto,
    user?: JwtPayload,
  ) {
    try {
      const plan = await this.repo.findByIdWithLimits(id)

      if (!plan) return null

      const { resourceLimits, ...planData } = updateSubscriptionPlanDto

      let updatedPlan: SubscriptionPlan | null

      if (resourceLimits !== undefined) {
        // Update plan with resource limits in a transaction
        const limitsData: Omit<PlanResourceLimitProps, 'subscriptionPlanId'>[] =
          []

        for (const limit of resourceLimits) {
          // Create new overage price for each resource limit
          const overagePrice = await this.stripeUseCase.createPrice({
            unit_amount: limit.overagePrice, // Price already in cents
            currency: 'usd',
            recurring: {
              interval:
                plan.getProps().period === Period.YEAR ? 'year' : 'month',
              usage_type: 'metered',
            },
            product_data: {
              name: `${plan.getProps().name} - ${limit.resourceType} Overage`,
              metadata: {
                planName: plan.getProps().name,
                priceType: UsageTypeUtils.generateOverageUsageType(
                  limit.resourceType as ResourceType,
                ),
              },
            },
          })

          limitsData.push({
            ...limit,
            overageStripePriceId: overagePrice.id,
            updatedBy: user?.userId,
          })
        }

        updatedPlan = await this.repo.updateWithLimits(
          id,
          {
            ...plan.getProps(),
            ...planData,
            updatedBy: user?.userId,
          },
          limitsData,
        )
      } else {
        // Update plan without changing resource limits
        updatedPlan = await this.repo.updateById(id, {
          ...plan.getProps(),
          ...planData,
          updatedBy: user?.userId,
        })
      }

      if (!updatedPlan) return null

      return updatedPlan.toResponse()
    } catch (error) {
      console.error(error)
      throw new HttpException(
        error instanceof Error
          ? error.message
          : 'Failed to update subscription plan',
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }

  async remove(id: Id) {
    try {
      const result = await this.repo.deleteWithLimits(id)
      return result
    } catch (error) {
      console.error(error)
      throw new HttpException(
        error instanceof Error
          ? error.message
          : 'Failed to delete subscription plan',
        HttpStatus.INTERNAL_SERVER_ERROR,
      )
    }
  }
}
