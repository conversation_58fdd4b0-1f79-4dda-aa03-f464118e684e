import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common'
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger'

import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { ApiPaginatedResponse } from '@backend/commons/decorators/paginate-response.decorator'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { AuthGuard } from '@backend/commons/guard/auth.guard'
import { PermissionsGuard } from '@backend/commons/guard/permissions.guards'
import { SubscriptionPlanUseCase } from '@backend/modules/subscription/usecases/subscription-plan.usecase'
import {
  CreateSubscriptionPlanDto,
  UpdateSubscriptionPlanDto,
} from '@backend/modules/subscription/applications/dto/subscription-plan.request.dto'
import { SubscriptionPlanResponseDto } from '@backend/modules/subscription/applications/dto/subscription-plan.response.dto'
import { CurrentUser } from '@backend/commons/decorators/current-user.decorator'

@ApiTags('Subscription Plan')
@Controller('subscription-plan')
export class SubscriptionPlanController {
  constructor(
    private readonly subscriptionPlanUseCase: SubscriptionPlanUseCase,
  ) {}

  @Post()
  @UseGuards(AuthGuard, PermissionsGuard)
  create(
    @Body() createSubscriptionPlanDto: CreateSubscriptionPlanDto,
    @CurrentUser() user: JwtPayload,
  ) {
    return this.subscriptionPlanUseCase.create(createSubscriptionPlanDto, user)
  }

  @Get()
  @ApiBearerAuth()
  @ApiPaginatedResponse(SubscriptionPlanResponseDto)
  @UseGuards(AuthGuard, PermissionsGuard)
  find(@Query() paginateOption: PaginateOptionsDto) {
    return this.subscriptionPlanUseCase.find(paginateOption)
  }

  @ApiResponse({
    type: SubscriptionPlanResponseDto,
  })
  @Get(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  findOne(@Param('id') id: string) {
    return this.subscriptionPlanUseCase.findOne(id)
  }

  @Patch(':id')
  @UseGuards(AuthGuard)
  update(
    @Param('id') id: string,
    @Body() updateSubscriptionPlanDto: UpdateSubscriptionPlanDto,
  ) {
    return this.subscriptionPlanUseCase.update(id, updateSubscriptionPlanDto)
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.subscriptionPlanUseCase.remove(id)
  }
}
