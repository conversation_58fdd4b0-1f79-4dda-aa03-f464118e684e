import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
  UseGuards,
  ForbiddenException,
} from '@nestjs/common'
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger'

import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { ApiPaginatedResponse } from '@backend/commons/decorators/paginate-response.decorator'
import { CurrentUser } from '@backend/commons/decorators/current-user.decorator'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { AuthGuard } from '@backend/commons/guard/auth.guard'
import { PermissionsGuard } from '@backend/commons/guard/permissions.guards'
import {
  PermissionAction,
  PermissionScope,
  PermissionType,
} from '@backend/cores/rbac/permission.constant'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'
import { OrgTeamHeaders } from '@backend/commons/decorators/org-team-headers.decorator'
import { CheckPermissions } from '@backend/commons/decorators/check-permission.decorator'
import { SubscriptionPlanResponseDto } from '@backend/modules/subscription/applications/dto/subscription-plan.response.dto'
import { SubscriptionDetailResponseDto } from '@backend/modules/subscription/applications/dto/subscription-detail.response.dto'
import { SubscriptionManagementUseCase } from '@backend/modules/subscription/usecases/subscription-management.usecase'
import { SubscriptionCheckoutUseCase } from '@backend/modules/subscription/usecases/subscription-checkout.usecase'
import { SubscriptionDetailsUseCase } from '@backend/modules/subscription/usecases/subscription-details.usecase'
import { SubscriptionPlanChangeUseCase } from '@backend/modules/subscription/usecases/subscription-plan-change.usecase'
import { CreateSubscriptionDto } from '@backend/modules/subscription/applications/dto/subscription.request.dto'
import { CreateCheckoutSessionDto } from '@backend/modules/subscription/applications/dto/create-checkout-session.dto'
import {
  ChangePlanDto,
  ChangePlanResponseDto,
} from '@backend/modules/subscription/applications/dto/change-plan.dto'
import {
  ChangePlanPreviewRequestDto,
  ChangePlanPreviewResponseDto,
} from '@backend/modules/subscription/applications/dto/change-plan-preview.dto'

@ApiTags('Subscription')
@Controller('subscription')
export class SubscriptionController {
  constructor(
    private readonly subscriptionManagementUseCase: SubscriptionManagementUseCase,
    private readonly subscriptionCheckoutUseCase: SubscriptionCheckoutUseCase,
    private readonly subscriptionDetailsUseCase: SubscriptionDetailsUseCase,
    private readonly subscriptionPlanChangeUseCase: SubscriptionPlanChangeUseCase,
  ) {}

  @Post('create-checkout-session')
  @ApiBearerAuth()
  @ApiResponse({
    status: 201,
    description: 'Successfully created Stripe Checkout Session.',
    schema: {
      type: 'object',
      properties: {
        sessionId: { type: 'string', example: 'cs_test_a1...' },
        url: {
          type: 'string',
          example: 'https://checkout.stripe.com/c/pay/cs_test_a1...',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request (e.g., missing planId, user email missing)',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiResponse({
    status: 500,
    description: 'Internal server error creating session',
  })
  @UseGuards(AuthGuard)
  async createCheckoutSession(
    @CurrentUser() user: JwtPayload,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
    @Body() createCheckoutSessionDto: CreateCheckoutSessionDto,
  ): Promise<{ sessionId: string; url: string | null }> {
    return this.subscriptionCheckoutUseCase.createCheckoutSession(
      user.userId,
      createCheckoutSessionDto.planId,
      orgTeamParams.organizationId,
    )
  }

  @Post('plan-change-preview')
  @ApiBearerAuth()
  @ApiResponse({
    status: 201,
    description: 'Successfully generated plan change preview.',
    type: ChangePlanPreviewResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request (e.g., invalid plan ID, same plan selected)',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  @ApiResponse({ status: 404, description: 'Plan or subscription not found' })
  @ApiResponse({
    status: 500,
    description: 'Internal server error during preview generation',
  })
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.BILLING,
      type: PermissionType.ORGANIZATION,
    },
  ])
  async changePlanPreview(
    @CurrentUser() user: JwtPayload,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
    @Body() changePlanPreviewDto: ChangePlanPreviewRequestDto,
  ): Promise<ChangePlanPreviewResponseDto> {
    return this.subscriptionPlanChangeUseCase.changePlanPreview(
      user.userId,
      orgTeamParams.organizationId,
      changePlanPreviewDto.newPlanId,
    )
  }

  @Post('plan-change')
  @ApiBearerAuth()
  @ApiResponse({
    status: 201,
    description: 'Successfully changed subscription plan.',
    type: ChangePlanResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request (e.g., invalid plan ID, same plan selected)',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  @ApiResponse({ status: 404, description: 'Plan or subscription not found' })
  @ApiResponse({
    status: 500,
    description: 'Internal server error during plan change',
  })
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.BILLING,
      type: PermissionType.ORGANIZATION,
    },
  ])
  async changePlan(
    @CurrentUser() user: JwtPayload,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
    @Body() changePlanDto: ChangePlanDto,
  ): Promise<ChangePlanResponseDto> {
    return this.subscriptionPlanChangeUseCase.initiatePlanChange(
      user.userId,
      orgTeamParams.organizationId,
      changePlanDto.newPlanId,
    )
  }

  @ApiResponse({
    status: 200,
    description:
      'Current subscription details with usage, limits, and billing information',
    type: SubscriptionDetailResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  @ApiResponse({ status: 404, description: 'No active subscription found' })
  @Get('/current-subscription')
  @ApiBearerAuth()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.BILLING,
      type: PermissionType.ORGANIZATION,
    },
  ])
  @ApiOperation({
    summary: 'Get current subscription details',
    description:
      'Returns comprehensive subscription information including plan details, resource limits, current usage, overage status, and transactional usage billing (SMS, phone calls)',
  })
  getCurrentSubscription(
    @CurrentUser() user: JwtPayload,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ): Promise<SubscriptionDetailResponseDto | null> {
    return this.subscriptionDetailsUseCase.getSubscriptionDetails(
      orgTeamParams.organizationId,
    )
  }

  @Get()
  @ApiBearerAuth()
  @ApiPaginatedResponse(SubscriptionPlanResponseDto)
  @UseGuards(AuthGuard, PermissionsGuard)
  find(
    @Query() paginateOption: PaginateOptionsDto,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    return this.subscriptionManagementUseCase.find(
      paginateOption,
      orgTeamParams,
    )
  }

  @Get('organization/:id')
  @ApiBearerAuth()
  @ApiResponse({
    status: 200,
    description: 'Subscription found successfully',
    type: SubscriptionPlanResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  @ApiResponse({ status: 404, description: 'Subscription not found' })
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.BILLING,
      type: PermissionType.ORGANIZATION,
    },
  ])
  findSubscriptionByOrganizationId(
    @CurrentUser() user: JwtPayload,
    @Param('id') organizationId: string,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    // Security: Verify user belongs to the requested organization
    if (orgTeamParams.organizationId !== organizationId) {
      throw new ForbiddenException('Access denied to this organization')
    }

    return this.subscriptionManagementUseCase.findSubscriptionsByOrganizationId(
      organizationId,
    )
  }

  @ApiResponse({
    type: SubscriptionPlanResponseDto,
  })
  @Get(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  findOne(@Param('id') id: string) {
    return this.subscriptionManagementUseCase.findOne(id)
  }

  @Patch(':id')
  @UseGuards(AuthGuard)
  update(
    @Param('id') id: string,
    @Body() updateSubscriptionDto: Partial<CreateSubscriptionDto>,
  ) {
    return this.subscriptionManagementUseCase.update(id, updateSubscriptionDto)
  }
}
