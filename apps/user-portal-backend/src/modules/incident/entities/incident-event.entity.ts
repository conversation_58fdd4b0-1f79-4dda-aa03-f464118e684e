import { z } from 'zod'
import {
  IncidentEventDetailType,
  IncidentEventType,
} from '@libs/shared/constants/incident'
import { CheckStatus } from '@libs/shared/constants/check.enum'

import { generateId } from '@backend/commons/id'
import { Id } from '@backend/cores/base/id.type'
import { AggregateRoot } from '@backend/cores/base/aggregate-root.base'

const IncidentEventSchema = z.object({
  incidentId: z.string().optional(),
  type: z.nativeEnum(IncidentEventType).optional(),
  user: z
    .object({
      id: z.string(),
      firstName: z.string().optional(),
      lastName: z.string().optional(),
      avatar: z.string().optional(),
    })
    .optional(),
  comment: z.string().optional(),
  attachment: z.string().optional(),
  attribute: z
    .object({
      type: z.nativeEnum(IncidentEventDetailType).optional(),
      value: z.object({
        location: z.string().optional(),
        message: z.string().optional(),
        waitingTime: z.number().optional(),
        receiver: z.string().optional(),
      }),
    })
    .optional(),
})

export type IncidentEventProps = z.infer<typeof IncidentEventSchema>

export const IncidentEventResponse = IncidentEventSchema.partial()

export interface IncidentEventResponse
  extends z.infer<typeof IncidentEventResponse> {
  id: Id
  createdAt: Date
  updatedAt: Date
}

export class IncidentEvent extends AggregateRoot<
  IncidentEventProps,
  IncidentEventResponse
> {
  static create(props: IncidentEventProps) {
    const incidentProps = {
      ...props,
      status: CheckStatus.PENDING,
    }

    const incident = new IncidentEvent({
      id: generateId(),
      props: incidentProps,
    })

    return incident
  }

  static update({ id, props }: { id: Id; props: IncidentEventProps }) {
    const incident = new IncidentEvent({
      id: id,
      props: props,
    })

    return incident
  }

  public toResponse(): IncidentEventResponse {
    const props = this.getProps()

    return {
      id: props.id,
      incidentId: props.incidentId,
      type: props.type,
      attribute: props.attribute,
      updatedAt: props.updatedAt,
      createdAt: props.createdAt,
    }
  }
}
