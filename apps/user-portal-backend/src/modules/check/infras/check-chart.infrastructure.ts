import { Inject, Injectable, NotFoundException } from '@nestjs/common'
import { REDIS_CHECK_PREFIX } from '@libs/shared/constants/key-prefix'

import { Id } from '@backend/cores/base/id.type'
import { CacheService } from '@backend/frameworks/cache/cache.service'
import { InfluxChartService } from '@backend/frameworks/influx/influx-chart.service'

import CheckChartRepository from '../applications/check-chart.repository'
import {
  CheckChartResponseTime,
  CheckChartResponseTimeRequest,
  CheckChartUpDown,
  CheckChartUpDownRequest,
} from '../interfaces/check-chart.interface'
import IncidentData from '../entities/incident-check.data'

interface TimeBlock {
  startAt: Date
  endAt: Date
  totalDowntime: number
}

@Injectable()
export class CheckChartInfrastructure implements CheckChartRepository {
  constructor(
    private readonly chartService: InfluxChartService,
    @Inject(CacheService) private readonly cache: CacheService,
  ) {}

  async getResponseTimeData(
    checkId: Id,
    responseTimeRequest: CheckChartResponseTimeRequest,
  ): Promise<CheckChartResponseTime[]> {
    try {
      const { groupTime, timeRange, location, percentile } = responseTimeRequest
      const workerId = location
      const checkCnf = await this.cache.get(
        checkId,
        REDIS_CHECK_PREFIX.CHECK_DATA,
      )
      if (!checkCnf) {
        throw new NotFoundException('Check not found')
      }
      return this.chartService.queryResponseTimeData(checkId, {
        groupTime,
        timeRange,
        workerId,
        percentile,
        measurement: 'CHECK_HEALTH',
      })
    } catch (error) {
      console.error(error)
      throw error
    }
  }

  async getUpDownData(
    checkId: string,
    upDownRequest: CheckChartUpDownRequest,
  ): Promise<CheckChartUpDown[]> {
    try {
      const timeBlocks: TimeBlock[] = []
      const now = new Date()
      const timeRangeStr = upDownRequest.timeRange
      const timeRange = parseInt(timeRangeStr.match(/\d+/)?.[0] || '0', 10)
      const checkCreatedAt = new Date(upDownRequest.checkCreatedAt)

      // Calculate which day to start based on timeRange or checkCreatedAt, whichever is more logical
      const maxStartDate = new Date(now)
      maxStartDate.setDate(now.getDate() - timeRange + 1)
      maxStartDate.setHours(0, 0, 0, 0)

      let currentStart = new Date(
        Math.max(checkCreatedAt.getTime(), maxStartDate.getTime()),
      )
      const loopEndDate = new Date(now)

      // Query incidents in the given time range
      const incidentsInTimeRange =
        await IncidentData.getIncidentByCheckIdInTimeRange(
          checkId,
          currentStart,
        )

      // console.log('Start Day: ', currentStart)
      // console.log('Incident list: ', incidentsInTimeRange)

      // Initialize timeBlocks
      while (currentStart <= loopEndDate) {
        let currentEnd = new Date(currentStart)
        currentEnd.setHours(23, 59, 59, 999)

        if (currentStart.getTime() === checkCreatedAt.getTime()) {
          currentStart = new Date(checkCreatedAt)
        }
        if (currentEnd > loopEndDate) {
          currentEnd = new Date(loopEndDate)
        }

        // Calculate downtime for this block
        let totalDowntime = 0

        for (const incident of incidentsInTimeRange) {
          const incidentStart = new Date(incident.startedAt)
          const incidentEnd = incident.resolvedAt
            ? new Date(incident.resolvedAt)
            : now // If unresolved, assume it lasts until now

          // Find overlapping time range between incident and current block
          const overlapStart = new Date(
            Math.max(currentStart.getTime(), incidentStart.getTime()),
          )
          const overlapEnd = new Date(
            Math.min(currentEnd.getTime(), incidentEnd.getTime()),
          )

          if (overlapStart < overlapEnd) {
            totalDowntime += overlapEnd.getTime() - overlapStart.getTime()
          }
        }

        timeBlocks.push({
          startAt: new Date(currentStart),
          endAt: new Date(currentEnd),
          totalDowntime: totalDowntime / 1000, // Convert to seconds
        })

        currentStart.setDate(currentStart.getDate() + 1)
        currentStart.setHours(0, 0, 0, 0)
      }

      // console.log(timeBlocks)
      return timeBlocks.map((block) => ({
        date: block.endAt,
        downTimeInSecond: block.totalDowntime,
      }))
    } catch (error) {
      console.error('Error generating time blocks:', error)
      throw error
    }
  }
}
