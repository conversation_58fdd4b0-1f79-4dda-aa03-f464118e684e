import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common'
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger'

import { PaginateOptionsDto } from '@backend/commons/dto/paginateOptions.dto'
import { ApiPaginatedResponse } from '@backend/commons/decorators/paginate-response.decorator'
import { CurrentUser } from '@backend/commons/decorators/current-user.decorator'
import { JwtPayload } from '@backend/cores/auth/auth.interface'
import { AuthGuard } from '@backend/commons/guard/auth.guard'
import { OrgTeamHeadersDto } from '@backend/commons/dto/org-team-headers.dto'
import { PermissionsGuard } from '@backend/commons/guard/permissions.guards'
import { CheckPermissions } from '@backend/commons/decorators/check-permission.decorator'
import {
  PermissionAction,
  PermissionScope,
  PermissionType,
} from '@backend/cores/rbac/permission.constant'
import { OrgTeamHeaders } from '@backend/commons/decorators/org-team-headers.decorator'

import { CheckResponseDto } from '../applications/dto/check.response.dto'
import {
  CreateCheckDto,
  UpdateCheckDto,
} from '../applications/dto/check.request.dto'
import { CheckUseCase } from '../usecases/check.usecase'
import {
  ResponseTimeRequestDto,
  UpDownRequestDto,
} from '../applications/dto/check-chart.request.dto'
import { CheckChartUseCase } from '../usecases/check-chart.usecase'
import {
  TestCheckDto,
  TestCheckResponseDto,
} from '../applications/dto/test-check.dto'

@ApiTags('Check')
@Controller('check')
export class CheckController {
  constructor(
    private readonly checkUseCase: CheckUseCase,
    private readonly checkChartUseCase: CheckChartUseCase,
  ) {}

  @Post('test')
  @UseGuards(AuthGuard)
  @ApiPaginatedResponse(TestCheckResponseDto)
  testCheck(@Body() testCheckRequest: TestCheckDto) {
    return this.checkUseCase.testWhenConfig(testCheckRequest)
  }

  @Get('locations')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  findWorker() {
    return this.checkUseCase.findAllWorkers()
  }

  @Post()
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.CHECKS,
      type: PermissionType.TEAM,
    },
  ])
  // @CheckSubscriptionLimit(LimitationType.CHECKS)
  create(
    @Body() createCheckDto: CreateCheckDto,
    @CurrentUser() user: JwtPayload,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    return this.checkUseCase.create(createCheckDto, user, orgTeamParams)
  }

  @Get()
  @ApiBearerAuth()
  @ApiPaginatedResponse(CheckResponseDto)
  @UseGuards(AuthGuard, PermissionsGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.CHECKS,
      type: PermissionType.TEAM,
    },
  ])
  find(
    @Query() paginateOption: PaginateOptionsDto,
    @OrgTeamHeaders() orgTeamParams: OrgTeamHeadersDto,
  ) {
    return this.checkUseCase.find(orgTeamParams, paginateOption)
  }

  @Get(':id/pause')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  pauseCheck(@Param('id') id: string) {
    return this.checkUseCase.pauseCheck(id)
  }

  @Get(':id/resume')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  unpauseCheck(@Param('id') id: string) {
    return this.checkUseCase.resumeCheck(id)
  }

  @ApiResponse({
    type: CheckResponseDto,
  })
  @Get(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @CheckPermissions([
    {
      action: PermissionAction.VIEW,
      scope: PermissionScope.CHECKS,
      type: PermissionType.TEAM,
    },
  ])
  findOne(@Param('id') id: string) {
    return this.checkUseCase.findOne(id)
  }

  @Patch(':id')
  @UseGuards(AuthGuard)
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.CHECKS,
    },
  ])
  update(@Param('id') id: string, @Body() updateCheckDto: UpdateCheckDto) {
    return this.checkUseCase.update(id, updateCheckDto)
  }

  @Delete(':id')
  @CheckPermissions([
    {
      action: PermissionAction.MANAGE,
      scope: PermissionScope.CHECKS,
    },
  ])
  remove(@Param('id') id: string) {
    return this.checkUseCase.remove(id)
  }

  @Post(':id/response-time')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  findResponseTime(
    @Param('id') id: string,
    @Body() responseTimeRequestDto: ResponseTimeRequestDto,
  ) {
    return this.checkChartUseCase.queryResponseTime(id, responseTimeRequestDto)
  }

  @Post(':id/updown-percentage')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  findUpDown(@Param('id') id: string, @Body() upDownRequest: UpDownRequestDto) {
    return this.checkChartUseCase.queryUpDownPercentage(id, upDownRequest)
  }
}
