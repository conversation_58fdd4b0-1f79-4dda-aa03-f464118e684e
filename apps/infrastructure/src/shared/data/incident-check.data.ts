import { DYNAMO_DB_TABLE } from '@libs/shared/constants/dynamo-constants'
import { model } from 'dynamoose'
import { customAlphabet } from 'nanoid'
import {
  IncidentItem,
  IncidentSchema,
} from '@libs/database/lib/dynamo/incident-check.schema'
import { EscalationPolicy } from '@libs/shared/constants/shared.interface'

const generateId = (size?: number) => {
  return customAlphabet('0123456789abcdefghijklmnopqrstuvwxyz', 12)(size)
}

const IncidentModel = model<IncidentItem>(
  DYNAMO_DB_TABLE.INCIDENTS,
  IncidentSchema,
  {
    create: false,
    waitForActive: false,
  },
)

export default class IncidentData {
  static async addIncident(body: Partial<IncidentItem>): Promise<IncidentItem> {
    const cleanedBody = JSON.parse(
      JSON.stringify(body, (_, value) => {
        if (value === null || value === undefined) {
          return undefined
        }
        return value
      }),
    )

    const newIncidentId = generateId()
    let mappedEscalationPolicy = {}

    if (body.escalationPolicy) {
      try {
        const parsedEscalationPolicy = JSON.parse(body.escalationPolicy)
        mappedEscalationPolicy = await this.mapEscalationPolicyToDynamo(
          parsedEscalationPolicy,
          newIncidentId,
        )
      } catch (error) {
        console.error('Failed to parse and map escalation policy:', error)
      }
    }

    const createdItem = await IncidentModel.create({
      ...cleanedBody,
      id: newIncidentId,
      startedAt: body.startedAt,
      escalationPolicy: JSON.stringify(mappedEscalationPolicy),
    })
    return createdItem
  }

  static async getIncidentById(id: string) {
    return IncidentModel.get(id)
  }

  static async updateIncident(id: string, data: Partial<IncidentItem>) {
    return IncidentModel.update({ id }, data)
  }

  private static async mapEscalationPolicyToDynamo(
    escalation: any,
    incidentId: string,
  ): Promise<EscalationPolicy> {
    const { teamId } = escalation

    return {
      repeat: [...new Array(escalation.repeatCount + 1)].map((_, i) => i + 1),
      repeatDelay: escalation.repeatDelay,
      incidentId,
      escalationSteps: escalation.escalationSteps.map((step) => ({
        stepDelay: step.stepDelay,
        severityId: step.severityId,
        incidentId,
        teamId,
        contacts: (step.escalationContacts || []).map((contact) => ({
          contactType: contact.contactType,
          contactId: contact.contactId,
          alerts: step.severity.alerts,
        })),
      })),
    }
  }
}
