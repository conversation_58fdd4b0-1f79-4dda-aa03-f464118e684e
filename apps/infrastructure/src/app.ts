import 'source-map-support/register'
import { App, StackProps as CdkStackProps } from 'aws-cdk-lib'
import { Construct } from 'constructs'
import { WebhookIntegrationStack } from '@infra/domains/webhooks/infra/stacks/webhook-integration-stack'
import { AuthorizerAPIGatewayStack } from '@infra/domains/auth/infra/stacks/api-gateway-authorizer-stack'

import { IncidentEscalationStack } from './domains/incidents/infra/stacks/incident-escalation-stack'
import { IncidentIntegrationCallbackStack } from './domains/incidents/infra/stacks/integration-callback-stack'
import { IncidentSESConfigSetStack } from './core/infra/stacks/ses-configuration-stack'
import { BillingStack } from './domains/billing/infra/stacks/billing-stack'
import { Environment } from './shared/environment'

export const createApp = (): App => {
  /*
    The AWS CDK team recommends to declare all stacks of all environments in the source code (Model all production stages in code - https://docs.aws.amazon.com/cdk/v2/guide/best-practices.html#best-practices-apps).
    This guide explains the approach in more detail (https://taimos.de/blog/deploying-your-cdk-app-to-different-stages-and-environments).
    Environment variables are used to fixate the account and region at synthesis time (https://docs.aws.amazon.com/cdk/v2/guide/environments.html).
    They can be set via the '.env' file or via the shell in use.
  */

  const app = new App()
  const serviceName = 'infrastructure'

  const nonProdAccount =
    process.env['CDK_NON_PROD_ACCOUNT'] ?? process.env['CDK_DEFAULT_ACCOUNT']
  if (!nonProdAccount) {
    throw new Error(
      `The Stage account isn't defined. Please set it via the '.env' file in the project directory.`,
    )
  }
  console.log('🚀 ~ createApp ~ nonProdAccount:', nonProdAccount)

  const nonProdRegion =
    process.env['CDK_STAGE_REGION'] ?? process.env['CDK_DEFAULT_REGION']
  if (!nonProdRegion) {
    throw new Error(
      `The Stage region isn't defined. Please set it via the '.env' file in the project directory.`,
    )
  }
  console.log('🚀 ~ createApp ~ nonProdRegion:', nonProdRegion)

  new EnvironmentStacks(app, Environment.NonProd, {
    environment: Environment.NonProd,
    serviceName,
    env: {
      account: nonProdAccount,
      region: nonProdRegion,
    },
  })

  const prodAccount =
    process.env['CDK_PROD_ACCOUNT'] ?? process.env['CDK_DEFAULT_ACCOUNT']
  if (!prodAccount) {
    throw new Error(
      `The Prod account isn't defined. Please set it via the '.env' file in the project directory.`,
    )
  }

  const prodRegion =
    process.env['CDK_PROD_REGION'] ?? process.env['CDK_DEFAULT_REGION']
  if (!prodRegion) {
    throw new Error(
      `The Prod region isn't defined. Please set it via the '.env' file in the project directory.`,
    )
  }

  new EnvironmentStacks(app, Environment.Prod, {
    environment: Environment.Prod,
    serviceName,
    env: {
      account: prodAccount,
      region: prodRegion,
    },
  })

  app.synth()

  return app
}

// Define StrictEnv for env property within StackProps
export interface StrictEnv {
  readonly account: string
  readonly region: string
}

// Update StackProps to use StrictEnv and alias cdk.StackProps
export interface StackProps extends Omit<CdkStackProps, 'env'> {
  environment: Environment
  serviceName: string
  env: StrictEnv // Use StrictEnv to enforce required account and region
}

export class EnvironmentStacks extends Construct {
  constructor(scope: Construct, id: string, props?: StackProps) {
    super(scope, id)

    // Add a guard to ensure props is defined and narrow its type
    if (!props) {
      throw new Error(
        `StackProps are required for ${id} in ${scope.node.path} but were not provided.`,
      )
    }

    console.log(
      // Use props.environment and props.serviceName directly as props is now guaranteed to be defined
      `Creating ${props.environment} environment stacks for ${props.serviceName} service`,
    )

    const incidentIntegrationStack = new IncidentIntegrationCallbackStack(
      this,
      'IncidentIntegrationCallbackStack',
      props, // props is now StackProps, not StackProps | undefined
    )

    new IncidentEscalationStack(
      this,
      'IncidentEscalationStack',
      incidentIntegrationStack.incidentIntegrationCallbackAPI,
      props, // props is now StackProps
    )

    new IncidentSESConfigSetStack(
      this,
      'IncidentSESConfigSetStack',
      incidentIntegrationStack.incidentIntegrationCallbackLambda,
      props, // props is now StackProps
    )

    // Create billing system (queues + Lambda functions + enrichment)
    new BillingStack(this, 'BillingStack', {
      environment: props.environment,
      env: props.env,
      eventBusArn: this.getEventBusArn(
        props.environment,
        props.env.region,
        props.env.account,
      ),
      rdsClusterArn: this.getRdsClusterArn(
        props.environment,
        props.env.region,
        props.env.account,
      ),
      rdsSecretArn: this.getRdsSecretArn(
        props.environment,
        props.env.region,
        props.env.account,
      ),
      rdsDatabaseName: this.getRdsDatabaseName(props.environment),
      serviceName: props.serviceName,
    })

    new WebhookIntegrationStack(this, 'WebhookIntegrationStack', props) // props is now StackProps
    new AuthorizerAPIGatewayStack(this, 'AuthorizerAPIGatewayStack', props) // props is now StackProps
  }

  /**
   * Get EventBridge event bus ARN for the environment
   * In a real deployment, these would be outputs from existing infrastructure stacks
   * or retrieved from SSM Parameter Store
   */
  private getEventBusArn(
    environment: Environment,
    region: string,
    account: string,
  ): string {
    const envPrefix = environment === Environment.Prod ? 'prod' : 'nonprod'
    return `arn:aws:events:${region}:${account}:event-bus/${envPrefix}-monitoring-dog-event-bus`
  }

  /**
   * Get RDS cluster ARN for the environment
   */
  private getRdsClusterArn(
    environment: Environment,
    region: string,
    account: string,
  ): string {
    const envPrefix = environment === Environment.Prod ? 'prod' : 'nonprod'
    return `arn:aws:rds:${region}:${account}:cluster:monitoring-dog-${envPrefix}-cluster`
  }

  /**
   * Get RDS secret ARN for the environment
   */
  private getRdsSecretArn(
    environment: Environment,
    region: string,
    account: string,
  ): string {
    const envPrefix = environment === Environment.Prod ? 'prod' : 'nonprod'
    // Note: The actual secret ARN will have a random suffix when created
    // This should be retrieved from the RDS stack outputs or SSM parameters
    return `arn:aws:secretsmanager:${region}:${account}:secret:/${envPrefix}/monitoring-dog/rds-credentials-XXXXXX`
  }

  /**
   * Get RDS database name for the environment
   */
  private getRdsDatabaseName(environment: Environment): string {
    return environment === Environment.Prod
      ? 'user-portal-prod-db'
      : 'user-portal-dev-db'
  }
}
