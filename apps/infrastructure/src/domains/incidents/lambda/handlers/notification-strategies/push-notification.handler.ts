import IncidentNotifier from '@libs/notifier/incident-notifier'
import {
  IncidentEventDetailType,
  IncidentEventType,
} from '@libs/shared/constants/incident'
import IncidentEventData from '@infra/shared/data/incident-event.data'

import { EscalationContact } from '../escalation-send-notification-handler'

import { getIncidentPayload } from './notification-utils'

export const handlePushNotification = async (body: EscalationContact) => {
  const slackIntegration = body.integrationSettings?.find(
    (integration) => integration.type === 'slack',
  )

  console.log('🚀 ~ slackIntegration:', slackIntegration)

  if (slackIntegration) {
    const notifier = new IncidentNotifier('slack')
    await notifier.started({
      ...getIncidentPayload(body.incident),
      incomingWebhookUrl: slackIntegration.config.incoming_webhook.url,
    })

    await Promise.all(
      body.users.map(async (user) => {
        await IncidentEventData.addIncidentEvent({
          type: IncidentEventType.NOTIFICATION,
          incidentId: body.incident.id,
          attribute: {
            type: IncidentEventDetailType.SEND_SLACK,
            value: {
              receiver: user.id,
            },
          },
        })
      }),
    )
  }

  const pushNotiIntegration = body.integrationSettings?.find(
    (integration) => integration.type === 'push_noti',
  )
  console.log('🚀 ~ pushNotiIntegration:', pushNotiIntegration)

  if (pushNotiIntegration) {
    const pushNotiNotifier = new IncidentNotifier('push_noti')
    await Promise.allSettled(
      body.users.map(async (user) => {
        await pushNotiNotifier.started({
          ...getIncidentPayload(body.incident),
          token: process.env['ONE_SIGNAL_AUTH_TOKEN']!,
          userId: user.id,
          firebaseId: user.firebaseId,
          teamId: body.teamId,
          organizationName: '',
          checkName: body.incident.title,
        })
      }),
    )

    await Promise.all(
      body.users.map(async (user) => {
        await IncidentEventData.addIncidentEvent({
          type: IncidentEventType.NOTIFICATION,
          incidentId: body.incident.id,
          attribute: {
            type: IncidentEventDetailType.SEND_PUSH_NOTIFICATION,
            value: {
              receiver: user.id,
            },
          },
        })
      }),
    )
  }
}
