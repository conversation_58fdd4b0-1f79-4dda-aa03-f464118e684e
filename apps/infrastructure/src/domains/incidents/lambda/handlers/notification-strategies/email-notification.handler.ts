import IncidentNotifier from '@libs/notifier/incident-notifier'
import {
  IncidentEventDetailType,
  IncidentEventType,
} from '@libs/shared/constants/incident'
import IncidentEventData from '@infra/shared/data/incident-event.data'

import { EscalationContact } from '../escalation-send-notification-handler'

import { getIncidentPayload } from './notification-utils'

export const handleEmailNotification = async (body: EscalationContact) => {
  if (!body?.users?.length) return

  const notifier = new IncidentNotifier('email', {
    host: process.env['SES_HOST']!,
    port: Number(process.env['SES_PORT']),
    ignoreTLS: false,
    secure: false,
    auth: {
      user: process.env['SES_SMTP_USERNAME']!,
      pass: process.env['SES_SMTP_PASSWORD']!,
    },
  })

  await notifier.started({
    ...getIncidentPayload(body.incident),
    to: body.users.map((user) => ({
      email: user.email,
      userId: user.id,
    })),
    headers: {
      'X-SES-CONFIGURATION-SET': 'IncidentConfigurationSet',
    },
    from: '<EMAIL>',
  })

  console.log('🚀 ~ email sent:', body.users)

  const res = await Promise.all(
    body.users.map(async (user) => {
      await IncidentEventData.addIncidentEvent({
        type: IncidentEventType.NOTIFICATION,
        incidentId: body.incident.id,
        attribute: {
          type: IncidentEventDetailType.SEND_EMAIL,
          value: {
            receiver: user.email,
          },
        },
      })
    }),
  )

  console.log('🚀 ~ email event res:', res)
}
