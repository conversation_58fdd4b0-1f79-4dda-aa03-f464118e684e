import IncidentNotifier from '@libs/notifier/incident-notifier'
import {
  IncidentEventDetailType,
  IncidentEventType,
} from '@libs/shared/constants/incident'
import IncidentEventData from '@infra/shared/data/incident-event.data'

import { EscalationContact } from '../escalation-send-notification-handler'

import { getIncidentPayload } from './notification-utils'

export const handleCallNotification = async (body: EscalationContact) => {
  console.log('Starting CALL alert processing', body)
  if (!body?.users?.length) {
    console.log('No users found, skipping CALL alert')
    return // Return instead of continue as this is a standalone function
  }

  const notifier = new IncidentNotifier('voice_call')
  const phoneServiceConfig = {
    token: process.env['STRINGEE_AUTH_TOKEN'] ?? '',
    fromNumber: process.env['STRINGEE_FROM_NUMBER'] ?? '',
    IncidentEventCallbackUrl: process.env['INCIDENT_EVENT_CALLBACK_URL'] ?? '',
    AckEscalateCallbackUrl: process.env['ACK_ESCALATE_CALLBACK_URL'] ?? '',
  }
  if (!phoneServiceConfig.token || !phoneServiceConfig.fromNumber) {
    console.error('Stringee credentials missing in environment variables')
    return
  }

  if (
    !phoneServiceConfig.IncidentEventCallbackUrl ||
    !phoneServiceConfig.AckEscalateCallbackUrl
  ) {
    console.error('Callback URLs missing in environment variables')
    return
  }

  console.log('Stringee call configuration:', {
    token: phoneServiceConfig.token ? 'present' : 'missing',
    fromNumber: phoneServiceConfig.fromNumber,
    callbackUrls: {
      incident: phoneServiceConfig.IncidentEventCallbackUrl,
      ack: phoneServiceConfig.AckEscalateCallbackUrl,
    },
  })

  const callResults = await Promise.allSettled(
    body.users.map(async (user) => {
      console.log('Attempting call to:', {
        userId: user.id,
        phoneNumber: user.phoneNumber,
        name: `${user.firstName} ${user.lastName}`,
      })

      const result = await notifier.started({
        ...getIncidentPayload(body.incident),
        token: phoneServiceConfig.token,
        from: phoneServiceConfig.fromNumber,
        to: user.phoneNumber,
        userId: user.id,
        serviceName: body.incident.checkInfo.url || '',
        // TODO1
        firstName: user.firstName,
        lastName: user.lastName,
        IncidentEventCallbackUrl: `${phoneServiceConfig.IncidentEventCallbackUrl}voice_call/events`,
        AckEscalateCallbackUrl: `${phoneServiceConfig.AckEscalateCallbackUrl}voice_call/events`,
      })

      console.log('Call result for user', user.id, ':', result)
      return result
    }),
  )

  console.log('All call results:', callResults)

  await Promise.all(
    body.users.map(async (user) => {
      await IncidentEventData.addIncidentEvent({
        type: IncidentEventType.NOTIFICATION,
        incidentId: body.incident.id,
        attribute: {
          type: IncidentEventDetailType.CALLING,
          value: {
            receiver: user.phoneNumber,
          },
        },
      })
    }),
  )
}
