import IncidentNotifier from '@libs/notifier/incident-notifier'
import {
  IncidentEventDetailType,
  IncidentEventType,
} from '@libs/shared/constants/incident'
import IncidentEventData from '@infra/shared/data/incident-event.data'

import { EscalationContact } from '../escalation-send-notification-handler'

import { getIncidentPayload } from './notification-utils'

export const handleSmsNotification = async (body: EscalationContact) => {
  console.log('Starting CALL alert processing', body)
  if (!body?.users?.length) {
    console.log('No users found, skipping CALL alert')
    return // Return instead of continue as this is a standalone function
  }

  const notifier = new IncidentNotifier('sms')
  const phoneServiceConfig = {
    token: process.env['STRINGEE_AUTH_TOKEN'] ?? '',
    brandNameId: process.env['STRINGEE_BRAND_ID'] ?? '',
  }
  if (!phoneServiceConfig.token || !phoneServiceConfig.brandNameId) {
    console.error('Stringee credentials missing in environment variables')
    return
  }

  console.log('Stringee call configuration:', {
    token: phoneServiceConfig.token ? 'present' : 'missing',
    brandNameId: phoneServiceConfig.brandNameId,
  })

  const callResults = await Promise.allSettled(
    body.users.map(async (user) => {
      console.log('Attempting call to:', {
        userId: user.id,
        phoneNumber: user.phoneNumber,
        name: `${user.firstName} ${user.lastName}`,
      })

      const result = await notifier.started({
        ...getIncidentPayload(body.incident),
        token: phoneServiceConfig.token,
        from: phoneServiceConfig.brandNameId,
        to: user.phoneNumber,
        userId: user.id,
        serviceName: body.incident.checkInfo.url || '',
        firstName: user.firstName,
        lastName: user.lastName,
      })

      console.log('Sms result for user', user.id, ':', result)
      return result
    }),
  )

  console.log('All sms results:', callResults)

  await Promise.all(
    body.users.map(async (user) => {
      await IncidentEventData.addIncidentEvent({
        type: IncidentEventType.NOTIFICATION,
        incidentId: body.incident.id,
        attribute: {
          type: IncidentEventDetailType.SEND_MESSAGE,
          value: {
            receiver: user.phoneNumber,
          },
        },
      })
    }),
  )
}
