export const getIncidentUrl = (teamId: string, incidentId: string) =>
  `${process.env['WEB_PORTAL_URL']}/team/${teamId}/incidents/${incidentId}`

export const getCheckUrl = (teamId: string, checkId: string) =>
  `${process.env['WEB_PORTAL_URL']}/team/${teamId}/check/${checkId}`

export const getIncidentPayload = (incident: any) => {
  return {
    incidentId: incident.id,
    url: incident.checkInfo.url || '',
    checkUrl: getCheckUrl(incident.teamId, incident.id), // Corrected: incident.id was incident.checkId
    incidentUrl: getIncidentUrl(incident.teamId, incident.id),
    title: incident.checkInfo.url || '',
    startedAt: new Date(incident.startedAt),
    cause: incident.cause,
    method: incident.checkInfo.method,
    metadata: {
      incidentId: incident.id,
    },
  }
}
