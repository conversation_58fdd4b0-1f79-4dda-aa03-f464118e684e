/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { <PERSON><PERSON>, Context, EventBridgeEvent } from 'aws-lambda'
import Stripe from 'stripe'

import { handleEmailNotification } from './notification-strategies/email-notification.handler'
import { handlePushNotification } from './notification-strategies/push-notification.handler'
import { handleCallNotification } from './notification-strategies/call-notification.handler'
import { handleAppCallNotification } from './notification-strategies/app-call-notification.handler'
import { handleSmsNotification } from './notification-strategies/sms-notification.handler'

export enum SeverityAlert {
  CALL = 'call',
  APP_CALL = 'app_call',
  SMS = 'sms',
  EMAIL = 'email',
  PUSH_NOTIFICATION = 'push_notification',
}

export interface EscalationContact {
  contactType: string
  contactId?: string
  teamId: string
  alerts: SeverityAlert[]
  users: {
    id: string
    email: string
    phoneNumber: string // Keep for potential SMS or fallback?
    firstName: string
    lastName: string
    firebaseId: string
    plivoEndpoint?: string // Add the Plivo endpoint from enrichment
  }[]
  incident: any
  integrationSettings: any[]
  // Enhanced billing context from enrichment
  billingContext?: {
    organizationId: string
    stripeCustomerId?: string
    isBillable: boolean
    entitlementStatus: boolean
    stripePriceId?: string
    usageType?: string
  }
  metadata?: {
    traceId: string
    timestamp: string
    notificationId?: string
  }
}

interface NotificationResult {
  success: boolean
  providerId?: string
  timestamp: string
  error?: string
  latency: number
}

interface BillingResult {
  success: boolean
  stripeEventId?: string
  error?: string
  idempotencyKey: string
  latency: number
}

const stripe = new Stripe(process.env['STRIPE_SECRET_KEY'] || '', {
  apiVersion: '2025-02-24.acacia',
})

export const handler: Handler = async (
  event: EventBridgeEvent<string, EscalationContact>,
  context: Context,
) => {
  context.callbackWaitsForEmptyEventLoop = false
  const startTime = Date.now()

  const body = event.detail
  const traceId =
    body.metadata?.traceId ||
    `trace-${Date.now()}-${Math.random().toString(36).substring(7)}`

  console.log(
    '🚀 Processing escalation notification with time-sensitive billing',
    {
      traceId,
      alertCount: body.alerts.length,
      organizationId: body.billingContext?.organizationId,
      isBillable: body.billingContext?.isBillable,
    },
  )

  try {
    const notificationResults: NotificationResult[] = []

    for (const alert of body.alerts) {
      console.log('Processing alert:', alert, { traceId })

      const notificationStartTime = Date.now()
      let notificationResult: NotificationResult

      // Process notification with billing integration
      const billingPromise = isBillableAlert(alert)
        ? processBillingDirectly(body, alert, traceId)
        : Promise.resolve(null)

      try {
        // Send notification (critical path)
        switch (alert) {
          case SeverityAlert.EMAIL:
            await handleEmailNotification(body)
            notificationResult = {
              success: true,
              timestamp: new Date().toISOString(),
              latency: Date.now() - notificationStartTime,
            }
            break
          case SeverityAlert.PUSH_NOTIFICATION:
            await handlePushNotification(body)
            notificationResult = {
              success: true,
              timestamp: new Date().toISOString(),
              latency: Date.now() - notificationStartTime,
            }
            break
          case SeverityAlert.CALL:
            await handleCallNotification(body)
            notificationResult = {
              success: true,
              timestamp: new Date().toISOString(),
              latency: Date.now() - notificationStartTime,
            }
            break
          case SeverityAlert.APP_CALL:
            await handleAppCallNotification(body)
            notificationResult = {
              success: true,
              timestamp: new Date().toISOString(),
              latency: Date.now() - notificationStartTime,
            }
            break
          case SeverityAlert.SMS:
            await handleSmsNotification(body)
            notificationResult = {
              success: true,
              timestamp: new Date().toISOString(),
              latency: Date.now() - notificationStartTime,
            }
            break
          default:
            console.log('Unsupported alert type', alert)
            notificationResult = {
              success: false,
              error: `Unsupported alert type: ${alert}`,
              timestamp: new Date().toISOString(),
              latency: Date.now() - notificationStartTime,
            }
            break
        }

        notificationResults.push(notificationResult)

        // Handle billing result asynchronously (don't block)
        if (isBillableAlert(alert)) {
          await handleBillingCompletion(
            billingPromise,
            alert,
            notificationResult,
            traceId,
          )
        }
      } catch (notificationError: any) {
        console.error('Notification failed', {
          alert,
          error: notificationError.message,
          traceId,
        })

        notificationResult = {
          success: false,
          error: notificationError.message,
          timestamp: new Date().toISOString(),
          latency: Date.now() - notificationStartTime,
        }

        notificationResults.push(notificationResult)

        // Even if notification fails, don't bill for it
        // Cancel the billing promise if possible
      }
    }

    const totalLatency = Date.now() - startTime
    console.log('✅ Escalation notification completed', {
      traceId,
      totalLatency,
      successfulNotifications: notificationResults.filter((r) => r.success)
        .length,
      failedNotifications: notificationResults.filter((r) => !r.success).length,
    })

    return {
      ...event,
      results: notificationResults,
      metadata: { traceId, totalLatency },
    }
  } catch (error: any) {
    console.error('Handler error', { error: error.message, traceId })
    throw error
  }
}

/**
 * Check if alert type is billable (SMS, CALL)
 */
function isBillableAlert(alert: SeverityAlert): boolean {
  return [SeverityAlert.SMS, SeverityAlert.CALL].includes(alert)
}

/**
 * Process billing directly with Stripe API for time-sensitive requirements
 */
async function processBillingDirectly(
  escalationContact: EscalationContact,
  alert: SeverityAlert,
  traceId: string,
): Promise<BillingResult> {
  const billingStartTime = Date.now()
  const { billingContext } = escalationContact

  if (!billingContext?.isBillable || !billingContext.stripeCustomerId) {
    return {
      success: false,
      error: 'Not billable or missing customer ID',
      idempotencyKey: '',
      latency: Date.now() - billingStartTime,
    }
  }

  const idempotencyKey = `${traceId}-${alert}-${Date.now()}`

  try {
    console.log('🔄 Processing direct billing', {
      traceId,
      alert,
      organizationId: billingContext.organizationId,
      stripeCustomerId: billingContext.stripeCustomerId,
    })

    // Direct Stripe API call for immediate billing
    const eventName = `transactional_${alert.toLowerCase()}`
    const meterEvent = await stripe.billing.meterEvents.create(
      {
        event_name: eventName,
        payload: {
          stripe_customer_id: billingContext.stripeCustomerId,
          value: '1',
          timestamp: Math.floor(Date.now() / 1000).toString(),
        },
      },
      {
        idempotencyKey,
      },
    )

    const latency = Date.now() - billingStartTime
    console.log('✅ Direct billing successful', {
      traceId,
      meterEventId: meterEvent.identifier,
      latency,
    })

    return {
      success: true,
      stripeEventId: meterEvent.identifier,
      idempotencyKey,
      latency,
    }
  } catch (error: any) {
    const latency = Date.now() - billingStartTime
    console.error('❌ Direct billing failed', {
      traceId,
      error: error.message,
      latency,
      alert,
    })

    return {
      success: false,
      error: error.message,
      idempotencyKey,
      latency,
    }
  }
}

/**
 * Handle billing completion asynchronously
 */
function handleBillingCompletion(
  billingPromise: Promise<BillingResult | null>,
  alert: SeverityAlert,
  notificationResult: NotificationResult,
  traceId: string,
): void {
  billingPromise
    .then(async (billingResult) => {
      if (!billingResult) return

      console.log('📊 Billing completion logged', {
        traceId,
        alert,
        billingSuccess: billingResult.success,
        notificationSuccess: notificationResult.success,
      })
    })
    .catch(async (error) => {
      console.error('⚠️ Billing completion failed', {
        traceId,
        alert,
        error: error.message,
      })
    })
}
