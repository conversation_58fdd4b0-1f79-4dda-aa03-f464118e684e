import { Handler, Context, EventBridgeEvent } from 'aws-lambda'
import { Pool } from 'pg'
import IntegrationSettingData from '@infra/shared/data/integration-setting.data'
import IncidentData from '@infra/shared/data/incident-check.data'
import UserData from '@infra/shared/data/user.data'

enum EscalationContactType {
  ENTIRE_TEAM = 'entire_team',
  ON_CALL = 'on_call',
  MEMBER = 'member',
  INTEGRATION = 'integration',
}

enum SeverityAlert {
  CALL = 'call',
  SMS = 'sms',
  EMAIL = 'email',
  PUSH_NOTIFICATION = 'push_notification',
}

interface EscalationContact {
  contactType: EscalationContactType
  contactId?: string
  alerts: SeverityAlert[]
}

interface EscalationPolicy {
  repeat: number[]
  repeatDelay: number
  incidentId: string
  escalationSteps: {
    contactType: EscalationContactType
    contactId?: string
    contacts: EscalationContact[]
  }[]
}

interface Incident {
  incidentId: string
  teamId: string
}

const pool = new Pool({
  port: 5432,
  host: process.env['DB_HOST'],
  user: process.env['DB_USERNAME'],
  password: process.env['DB_PASSWORD'],
  database: process.env['DB_DATABASE'],
  max: 1,
  min: 0,
  idleTimeoutMillis: 120000,
  connectionTimeoutMillis: 10000,
})

export const handler: Handler = async (
  event: EventBridgeEvent<string, any>,
  context: Context,
) => {
  console.log('🚀 ~ event:', typeof event, JSON.stringify(event, null, 1))

  context.callbackWaitsForEmptyEventLoop = false

  const client = await pool.connect()

  try {
    const body = event?.[0] as Incident
    const { teamId, incidentId } = body

    const incident = await IncidentData.getIncidentById(incidentId)

    if (!incident) return null

    const userData = new UserData(client)
    const integrationSettingData = new IntegrationSettingData(client)

    console.log('🚀 ~ incident:', incident)

    const escalationPolicy = JSON.parse(
      incident.escalationPolicy,
    ) as EscalationPolicy

    console.log('🚀 ~ escalationPolicy:', escalationPolicy)

    // Flatten the escalation steps and merge with the alerts along with contactId and contactType
    const escalationSteps = escalationPolicy.escalationSteps
      .map((step) =>
        step.contacts.map((contact) => ({
          ...contact,
          alerts: escalationPolicy.repeat.map(() => contact.alerts).flat(),
        })),
      )
      .flat()

    console.log('🚀 ~ escalationSteps:', escalationSteps)

    let integrationSettings

    if (
      escalationSteps.some((step) =>
        step.alerts.includes(SeverityAlert.PUSH_NOTIFICATION),
      )
    ) {
      integrationSettings =
        await integrationSettingData.getIntegrationSettingByTeam(teamId)
    }

    console.log('🚀 ~ integrationSettings:', integrationSettings)

    const notification = await Promise.all(
      escalationSteps.map(async (step) => {
        const { contactId, contactType, alerts } = step

        switch (contactType) {
          case EscalationContactType.MEMBER: {
            if (!contactId) return {}
            const user = await userData.getUserById(contactId)

            return {
              ...body,
              contactId,
              contactType,
              alerts,
              ...(user && { users: [user] }),
              ...(integrationSettings && { integrationSettings }),
              incident,
            }
          }

          case EscalationContactType.ON_CALL: {
            const users = await userData.getOnCallUsers(teamId)

            console.log('🚀 ~ onCallUsers getUsers:', users)

            return {
              ...body,
              contactId,
              contactType,
              alerts,
              ...(users && { users }),
              ...(integrationSettings && { integrationSettings }),
              incident,
            }
          }

          case EscalationContactType.ENTIRE_TEAM: {
            const users = await userData.getUsersByTeamId(teamId)

            console.log('🚀 ~ entire teams:', users)

            return {
              ...body,
              contactId,
              contactType,
              alerts,
              ...(users && { users }),
              ...(integrationSettings && { integrationSettings }),
              incident,
            }
          }

          default:
            return body
        }
      }),
    )

    console.log('🚀 ~ notification:', JSON.stringify(notification))

    return notification
  } catch (error) {
    console.error(error)
    throw error
  } finally {
    client?.release(true)
  }
}
