import { Handler, Context, EventBridgeEvent } from 'aws-lambda'
import { Pool } from 'pg'
import IncidentData from '@infra/shared/data/incident-check.data'
import IntegrationSettingData from '@infra/shared/data/integration-setting.data'
import UserData from '@infra/shared/data/user.data'

enum EscalationContactType {
  ENTIRE_TEAM = 'entire_team',
  ON_CALL = 'on_call',
  MEMBER = 'member',
  INTEGRATION = 'integration',
}

enum SeverityAlert {
  CALL = 'call',
  APP_CALL = 'app_call',
  SMS = 'sms',
  EMAIL = 'email',
  PUSH_NOTIFICATION = 'push_notification',
}

interface EscalationContact {
  contactType: EscalationContactType
  contactId?: string
  incidentId: string
  teamId: string
  alerts: SeverityAlert[]
}

const pool = new Pool({
  port: 5432,
  host: process.env['DB_HOST'],
  user: process.env['DB_USERNAME'],
  password: process.env['DB_PASSWORD'],
  database: process.env['DB_DATABASE'],
  max: 1,
  min: 0,
  idleTimeoutMillis: 120000,
  connectionTimeoutMillis: 10000,
})

export const handler: Handler = async (
  event: EventBridgeEvent<string, any>,
  context: Context,
) => {
  console.log('🚀 ~ event:', event)

  context.callbackWaitsForEmptyEventLoop = false

  const client = await pool.connect()

  try {
    const body = JSON.parse(event[0].body) as EscalationContact

    console.log('🚀 ~ body:', body)

    const { contactId, teamId, incidentId, alerts } = body

    const userData = new UserData(client)
    const integrationSettingData = new IntegrationSettingData(client)

    const incident = await IncidentData.getIncidentById(incidentId)

    console.log('🚀 ~ incident:', incident)

    let integrationSettings

    if (alerts.includes(SeverityAlert.PUSH_NOTIFICATION)) {
      integrationSettings =
        await integrationSettingData.getIntegrationSettingByTeam(teamId)

      console.log('🚀 ~ integrationSettings:', integrationSettings)
    }

    switch (body.contactType) {
      case EscalationContactType.MEMBER: {
        if (!contactId) return {}
        const user = await userData.getUserById(contactId)
        console.log(
          '🚀 ~  EscalationContactType.MEMBER user:',
          JSON.stringify(user, null, 1),
        )

        return {
          ...body,
          ...(user && { users: [user] }),
          ...(integrationSettings && { integrationSettings }),
          incident,
        }
      }

      case EscalationContactType.ON_CALL: {
        const users = await userData.getOnCallUsers(teamId)

        console.log(
          '🚀 ~ EscalationContactType.ON_CALL getUsers:',
          JSON.stringify(users, null, 1),
        )

        return {
          ...body,
          ...(users && { users }),
          ...(integrationSettings && { integrationSettings }),
          incident,
        }
      }

      case EscalationContactType.ENTIRE_TEAM: {
        const users = await userData.getUsersByTeamId(teamId)

        console.log(
          '🚀 ~ EscalationContactType.ENTIRE_TEAM teams:',
          JSON.stringify(users, null, 1),
        )

        return {
          ...body,
          ...(users && { users }),
          ...(integrationSettings && { integrationSettings }),
          incident,
        }
      }

      default:
        return body
    }
  } catch (error) {
    console.error(error)
    throw error
  } finally {
    client?.release(true)
  }
}
