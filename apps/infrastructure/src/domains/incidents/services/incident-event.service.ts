import {
  IncidentEventType,
  IncidentEventDetailType,
} from '@libs/shared/constants/incident'
import { IncidentEventInterface } from '@libs/database/lib/dynamo/incident-event.schema'
import IncidentEventData from '@infra/shared/data/incident-event.data'

// Helper type for user information
export type UserInfo = {
  id: string
  firstName: string
  lastName: string
  avatar?: string
}

// Type for notification detail types for better type safety
export type NotificationDetailSubset =
  | IncidentEventDetailType.SEND_SLACK
  | IncidentEventDetailType.SEND_PUSH_NOTIFICATION
  | IncidentEventDetailType.SEND_EMAIL
  | IncidentEventDetailType.OPENED_EMAIL
  | IncidentEventDetailType.SEND_SMS
  | IncidentEventDetailType.CALLING
  | IncidentEventDetailType.ANSWERED_CALL
  | IncidentEventDetailType.SEND_MESSAGE
  | IncidentEventDetailType.REJECT_CALL
  | IncidentEventDetailType.CALL_FAILED
  | IncidentEventDetailType.NO_ANSWER

/**
 * Centralized service for creating incident events in a type-safe, maintainable way.
 */
export class IncidentEventService {
  /**
   * Private underlying method to create any incident event.
   * @param params - Incident event creation parameters
   */
  private static async _createUnderlyingEvent({
    incidentId,
    type,
    user,
    attribute,
  }: {
    incidentId: string
    type: IncidentEventType
    user?: UserInfo
    attribute: {
      type: IncidentEventDetailType
      value?: any
    }
  }): Promise<IncidentEventInterface> {
    return IncidentEventData.addIncidentEvent({
      incidentId,
      type,
      ...(user && { user }),
      attribute,
    })
  }

  // --- System-driven Events ---

  /**
   * Creates an event for when an incident has started (typically by system).
   */
  public static async createIncidentStartedEvent(
    incidentId: string,
    value: { location?: string; message: string; [key: string]: any },
  ): Promise<IncidentEventInterface> {
    return this._createUnderlyingEvent({
      incidentId,
      type: IncidentEventType.SYSTEM,
      attribute: {
        type: IncidentEventDetailType.INCIDENT_STARTED,
        value,
      },
    })
  }

  /**
   * Creates an event for when an incident is resolved by the system (e.g., auto-resolve).
   */
  public static async createSystemResolvedEvent(
    incidentId: string,
    value?: { reason?: string; [key: string]: any },
  ): Promise<IncidentEventInterface> {
    return this._createUnderlyingEvent({
      incidentId,
      type: IncidentEventType.SYSTEM,
      attribute: {
        type: IncidentEventDetailType.RESOLVED,
        value,
      },
    })
  }

  /**
   * Creates an event for when an incident is marked as recovered by the system.
   */
  public static async createSystemRecoveredEvent(
    incidentId: string,
    value?: { [key: string]: any },
  ): Promise<IncidentEventInterface> {
    return this._createUnderlyingEvent({
      incidentId,
      type: IncidentEventType.SYSTEM,
      attribute: {
        type: IncidentEventDetailType.INCIDENT_RECOVERED,
        value,
      },
    })
  }

  /**
   * Creates an event for when the system is waiting for an incident to auto-resolve.
   */
  public static async createWaitingForAutoResolveEvent(
    incidentId: string,
    value?: { [key: string]: any },
  ): Promise<IncidentEventInterface> {
    return this._createUnderlyingEvent({
      incidentId,
      type: IncidentEventType.SYSTEM,
      attribute: {
        type: IncidentEventDetailType.WAITING_AUTO_RESOLVE,
        value,
      },
    })
  }

  /**
   * Creates an event for when an incident has reappeared (system detected).
   */
  public static async createSystemReappearedEvent(
    incidentId: string,
    value?: { [key: string]: any },
  ): Promise<IncidentEventInterface> {
    return this._createUnderlyingEvent({
      incidentId,
      type: IncidentEventType.SYSTEM, // Or CHECK if a check directly causes this
      attribute: {
        type: IncidentEventDetailType.REAPPEARED,
        value,
      },
    })
  }

  // --- User-driven / Manual Events ---

  /**
   * Creates an event for when a user acknowledges an incident.
   */
  public static async createUserAcknowledgedEvent(
    incidentId: string,
    user: UserInfo,
    value?: { [key: string]: any },
  ): Promise<IncidentEventInterface> {
    return this._createUnderlyingEvent({
      incidentId,
      type: IncidentEventType.MANUAL,
      user,
      attribute: {
        type: IncidentEventDetailType.ACKNOWLEDGED_INCIDENT,
        value,
      },
    })
  }

  /**
   * Creates an event for when a user manually resolves an incident.
   */
  public static async createUserResolvedEvent(
    incidentId: string,
    user?: UserInfo,
    value?: { [key: string]: any },
  ): Promise<IncidentEventInterface> {
    return this._createUnderlyingEvent({
      incidentId,
      type: IncidentEventType.MANUAL,
      ...(user && { user }),
      attribute: {
        type: IncidentEventDetailType.RESOLVED,
        value,
      },
    })
  }

  /**
   * Creates a comment event.
   */
  public static async createCommentEvent(
    incidentId: string,
    user: UserInfo,
    value: { comment: string; [key: string]: any },
  ): Promise<IncidentEventInterface> {
    return this._createUnderlyingEvent({
      incidentId,
      type: IncidentEventType.COMMENT,
      user,
      attribute: {
        type: IncidentEventDetailType.COMMENT,
        value,
      },
    })
  }

  /**
   * Creates an event for when a user escalates an incident.
   */
  public static async createUserEscalatedEvent(
    incidentId: string,
    user: UserInfo,
    value?: { reason?: string; [key: string]: any },
  ): Promise<IncidentEventInterface> {
    return this._createUnderlyingEvent({
      incidentId,
      type: IncidentEventType.MANUAL,
      user,
      attribute: {
        type: IncidentEventDetailType.ESCALATED,
        value,
      },
    })
  }

  /**
   * Creates a generic user-initiated event when specific methods are not suitable.
   */
  public static async createGenericUserEvent(
    incidentId: string,
    user: UserInfo,
    detailType: IncidentEventDetailType,
    value?: { [key: string]: any },
  ): Promise<IncidentEventInterface> {
    return this._createUnderlyingEvent({
      incidentId,
      type: IncidentEventType.MANUAL,
      user,
      attribute: {
        type: detailType,
        value,
      },
    })
  }

  // --- Check-driven Events ---

  /**
   * Creates an event for when a check receives an error.
   */
  public static async createCheckReceivedErrorEvent(
    incidentId: string,
    value: { error: string | object; checkName?: string; [key: string]: any },
  ): Promise<IncidentEventInterface> {
    return this._createUnderlyingEvent({
      incidentId,
      type: IncidentEventType.CHECK,
      attribute: {
        type: IncidentEventDetailType.RECEIVED_ERROR,
        value,
      },
    })
  }

  /**
   * Creates an event for when a check indicates recovery.
   */
  public static async createCheckRecoveredEvent(
    incidentId: string,
    value?: { checkName?: string; [key: string]: any },
  ): Promise<IncidentEventInterface> {
    return this._createUnderlyingEvent({
      incidentId,
      type: IncidentEventType.CHECK,
      attribute: {
        type: IncidentEventDetailType.INCIDENT_RECOVERED, // Using more specific INCIDENT_RECOVERED
        value,
      },
    })
  }

  /**
   * Creates an event for when a check indicates an issue has reappeared.
   */
  public static async createCheckReappearedEvent(
    incidentId: string,
    value?: { checkName?: string; [key: string]: any },
  ): Promise<IncidentEventInterface> {
    return this._createUnderlyingEvent({
      incidentId,
      type: IncidentEventType.CHECK,
      attribute: {
        type: IncidentEventDetailType.REAPPEARED,
        value,
      },
    })
  }

  /**
   * Creates an event for when a check initiates a recovery process.
   */
  public static async createCheckStartRecoveryEvent(
    incidentId: string,
    value?: { checkName?: string; [key: string]: any },
  ): Promise<IncidentEventInterface> {
    return this._createUnderlyingEvent({
      incidentId,
      type: IncidentEventType.CHECK,
      attribute: {
        type: IncidentEventDetailType.START_RECOVERY,
        value,
      },
    })
  }

  // --- Notification Events ---
  /**
   * Creates a notification-related event (e.g., email sent, call initiated).
   * @param userPerformingNotificationAction User who initiated the action leading to notification, or null if system.
   */
  public static async createNotificationEvent(
    incidentId: string,
    notificationDetailType: NotificationDetailSubset,
    userPerformingNotificationAction: UserInfo | null,
    value: {
      receiver?: string
      status?: string
      error?: string
      [key: string]: any
    },
  ): Promise<IncidentEventInterface> {
    return this._createUnderlyingEvent({
      incidentId,
      type: IncidentEventType.NOTIFICATION,
      ...(userPerformingNotificationAction && {
        user: userPerformingNotificationAction,
      }),
      attribute: {
        type: notificationDetailType,
        value,
      },
    })
  }

  /**
   * Creates a notification event triggered by a callback from an external provider (e.g., Plivo).
   */
  public static async createNotificationCallbackEvent(
    incidentId: string,
    callbackEventType: IncidentEventDetailType, // Type from the callback payload
    user?: UserInfo, // Optional user associated with the event
    value?: { [key: string]: any }, // Optional value from the callback payload
  ): Promise<IncidentEventInterface> {
    return this._createUnderlyingEvent({
      incidentId,
      type: IncidentEventType.NOTIFICATION,
      ...(user && { user }),
      attribute: {
        type: callbackEventType,
        value,
      },
    })
  }
}
