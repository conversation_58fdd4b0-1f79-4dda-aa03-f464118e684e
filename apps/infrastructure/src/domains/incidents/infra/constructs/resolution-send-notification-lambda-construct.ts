/* eslint-disable @typescript-eslint/no-non-null-assertion */
import * as cdk from 'aws-cdk-lib'
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb'
import { Construct } from 'constructs'
import * as iam from 'aws-cdk-lib/aws-iam'
import { StandardLambdaConstruct } from '@infra/shared/constructs/standard-lambda.construct'
import { SecretProvider } from '@infra/shared/constructs/secret-provider.construct'

import * as path from 'path'

export const createSendNotificationLambda = (
  scope: Construct,
  incidentTable: dynamodb.ITable,
  idSuffix: string,
  secretProvider: SecretProvider,
): StandardLambdaConstruct => {
  // Define the additional policies needed for this Lambda
  const additionalPolicies = [
    new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'logs:CreateLogGroup',
        'logs:CreateLogStream',
        'logs:PutLogEvents',
      ],
      resources: ['*'],
    }),
  ]

  // Create the Lambda function using the StandardLambdaConstruct
  const lambda = new StandardLambdaConstruct(
    scope,
    `IncidentResolvedNotification-${idSuffix}`,
    {
      entry: path.resolve(
        __dirname,
        '..',
        '..',
        'lambda',
        'handlers',
        'resolution-send-notification-handler.ts',
      ),
      handler: 'handler',
      additionalPolicies: additionalPolicies,
      logRetention: cdk.aws_logs.RetentionDays.ONE_WEEK,
      environment: {
        // Common vars from SecretProvider
        NODE_ENV: secretProvider.nodeEnv,
        LOG_LEVEL: secretProvider.appLogLevel,

        // DB vars from SecretProvider
        DB_HOST: secretProvider.dbHost,
        DB_USERNAME: secretProvider.dbUsername,
        DB_PASSWORD: secretProvider.dbPassword,
        DB_DATABASE: secretProvider.dbName,

        // SES vars from SecretProvider
        SES_SMTP_USERNAME: secretProvider.sesSmtpUsername,
        SES_SMTP_PASSWORD: secretProvider.sesSmtpPassword,
        SES_FROM_MAIL: secretProvider.sesFromAddress,
        SES_HOST: secretProvider.sesHost,
        SES_PORT: secretProvider.sesPort,

        // Application vars from SecretProvider
        WEB_PORTAL_URL: secretProvider.webPortalUrl,
      },
    },
  )

  // Grant the Lambda function read/write access to the incident table
  incidentTable.grantReadWriteData(lambda.lambdaFunction)

  // Return the construct instance so the caller can grant permissions for secrets
  return lambda
}
