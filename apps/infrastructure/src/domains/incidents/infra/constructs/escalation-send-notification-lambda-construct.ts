/* eslint-disable @typescript-eslint/no-non-null-assertion */
import * as cdk from 'aws-cdk-lib'
import { Construct } from 'constructs'
import * as iam from 'aws-cdk-lib/aws-iam'
import { StandardLambdaConstruct } from '@infra/shared/constructs/standard-lambda.construct'
import { SecretProvider } from '@infra/shared/constructs/secret-provider.construct'

import * as path from 'path'

interface NotificationLambdaProps {
  IncidentEventCallbackUrl: string
  AckEscalateCallbackUrl: string
  secretProvider: SecretProvider
}

export const createSendNotificationLambda = (
  scope: Construct,
  props: NotificationLambdaProps,
): StandardLambdaConstruct => {
  // Define the additional policies needed for this Lambda
  const additionalPolicies = [
    new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'logs:CreateLogGroup',
        'logs:CreateLogStream',
        'logs:PutLogEvents',
      ],
      resources: ['*'],
    }),
    new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'dynamodb:GetItem',
        'dynamodb:PutItem',
        'dynamodb:UpdateItem',
        'dynamodb:DeleteItem',
        'dynamodb:Query',
        'dynamodb:Scan',
        'dynamodb:CreateTable',
      ],
      resources: ['*'],
    }),
  ]

  // Create the Lambda function using the StandardLambdaConstruct
  const lambda = new StandardLambdaConstruct(scope, 'NotificationFunction', {
    entry: path.resolve(
      __dirname,
      '..',
      '..',
      'lambda',
      'handlers',
      'escalation-send-notification-handler.ts',
    ),
    handler: 'handler',
    additionalPolicies: additionalPolicies,
    logRetention: cdk.aws_logs.RetentionDays.ONE_WEEK,
    environment: {
      // Common vars from SecretProvider
      NODE_ENV: props.secretProvider.nodeEnv,
      LOG_LEVEL: props.secretProvider.appLogLevel,

      // SES vars from SecretProvider
      SES_SMTP_USERNAME: props.secretProvider.sesSmtpUsername,
      SES_SMTP_PASSWORD: props.secretProvider.sesSmtpPassword,
      SES_FROM_MAIL: props.secretProvider.sesFromAddress,
      SES_HOST: props.secretProvider.sesHost,
      SES_PORT: props.secretProvider.sesPort,

      // Application vars from SecretProvider
      WEB_PORTAL_URL: props.secretProvider.webPortalUrl,

      // Plivo vars from SecretProvider
      PLIVO_AUTH_ID: props.secretProvider.plivoAuthId,
      PLIVO_AUTH_TOKEN: props.secretProvider.plivoAuthToken,
      PLIVO_PHLO_ID: props.secretProvider.plivoPhloId,

      // Stringee vars from SecretProvider
      STRINGEE_AUTH_TOKEN: props.secretProvider.stringeeAuthToken,
      STRINGEE_FROM_NUMBER: props.secretProvider.stringeeFromNumber,

      // Callback URLs from props
      INCIDENT_EVENT_CALLBACK_URL: props.IncidentEventCallbackUrl,
      ACK_ESCALATE_CALLBACK_URL: props.AckEscalateCallbackUrl,
    },
    bundlingOptions: {
      externalModules: ['@react-email', 'react/jsx-runtime'],
      nodeModules: [
        'dynamoose',
        'nanoid',
        '@slack/webhook',
        'lodash',
        'nodemailer',
        'date-fns',
        'query-string',
        'plivo',
      ],
    },
  })

  return lambda
}
