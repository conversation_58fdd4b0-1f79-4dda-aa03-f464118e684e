import * as cdk from 'aws-cdk-lib'
import { Construct } from 'constructs'
import { PolicyStatement } from 'aws-cdk-lib/aws-iam'
import { RestApi, TokenAuthorizer } from 'aws-cdk-lib/aws-apigateway'
import { LogGroup, RetentionDays } from 'aws-cdk-lib/aws-logs'
import { CustomIncidentAPIStack } from '@infra/domains/auth/infra/stacks/custom-incident-endpoint-stack'
import { CheckAPIStack } from '@infra/domains/auth/infra/stacks/check-endpoint-stack'
import { StandardLambdaAuthorizerConstruct } from '@infra/shared/constructs/standard-lambda-authorizer.construct'
import { StandardLambdaConstruct } from '@infra/shared/constructs/standard-lambda.construct'
import { SecretProvider } from '@infra/shared/constructs/secret-provider.construct'
import { StackProps } from '@infra/app'

import path from 'path'

export class AuthorizerAPIGatewayStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: StackProps) {
    super(scope, id, props)

    // Instantiate SecretProvider
    const secretProvider = new SecretProvider(this, 'AuthGatewaySecrets', {
      environment: props.environment,
    })

    const customAuthorizerLogGroup = new LogGroup(
      this,
      'CustomAuthorizerLogGroup',
      {
        logGroupName: `/authorizer-apigateway/custom-authorizer`,
        retention: RetentionDays.ONE_WEEK, // Keep specific retention
        removalPolicy: cdk.RemovalPolicy.DESTROY,
      },
    )

    // Define policies needed by the authorizer lambda
    const networkPolicy = new PolicyStatement({
      actions: [
        'ec2:DescribeNetworkInterfaces',
        'ec2:CreateNetworkInterface',
        'ec2:DeleteNetworkInterface',
      ],
      resources: ['*'], // As per original
    })

    const logPolicy = new PolicyStatement({
      actions: ['logs:CreateLogStream', 'logs:PutLogEvents'],
      resources: [customAuthorizerLogGroup.logGroupArn], // Grant access to the specific log group
    })

    // Use the shared StandardLambdaConstruct
    const customAuthorizerLambdaConstruct = new StandardLambdaConstruct(
      this,
      'CustomAuthorizerLambda', // Keep logical ID similar
      {
        entry: path.join(
          __dirname,
          '../../lambda/handlers/authorizer-handler.ts',
        ),
        bundlingOptions: {
          nodeModules: ['pg', 'drizzle-orm', 'nanoid'],
        },
        environment: {
          // Common vars from SecretProvider
          NODE_ENV: secretProvider.nodeEnv,
          LOG_LEVEL: secretProvider.appLogLevel,

          // DB vars from SecretProvider
          DB_HOST: secretProvider.dbHost,
          DB_PORT: secretProvider.dbPort,
          DB_USERNAME: secretProvider.dbUsername,
          DB_PASSWORD: secretProvider.dbPassword,
          DB_DATABASE: secretProvider.dbName, // Mapped to dbName
        },
        additionalPolicies: [networkPolicy, logPolicy],
        logRetention: RetentionDays.ONE_WEEK, // Align construct's default log group retention just in case
      },
    )

    const api = new RestApi(this, 'AuthorizerGatewayApi', {
      restApiName: 'Authorizer Gateway API',
    })

    // Use the shared construct for the Lambda Authorizer
    const customAuthorizerConstruct = new StandardLambdaAuthorizerConstruct(
      this,
      'CustomAuthorizer',
      {
        authorizerLambdaFunction:
          customAuthorizerLambdaConstruct.lambdaFunction, // Use the function from the construct
        identitySource: 'method.request.header.Auth-Token', // Keep the original identity source
      },
    )
    // Retrieve the authorizer instance from the construct
    const customAuthorizer =
      customAuthorizerConstruct.authorizer as TokenAuthorizer

    // Grant secret read permissions to the authorizer lambda
    secretProvider.grantReadPermissionsTo(
      customAuthorizerLambdaConstruct.lambdaFunction,
    )

    // Grant the lambda function write access to the specific log group
    customAuthorizerLogGroup.grantWrite(
      customAuthorizerLambdaConstruct.lambdaFunction,
    )

    new CustomIncidentAPIStack(this, 'CustomIncidentAPIEndpoint', {
      api: api,
      authorizer: customAuthorizer,
    })

    new CheckAPIStack(this, 'CheckAPIEndpoint', {
      api: api,
      authorizer: customAuthorizer,
    })

    new cdk.CfnOutput(this, 'ApiUrl', { value: api.url })
  }
}
