import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda'
import { Pool } from 'pg'
import { IncidentStatus } from '@libs/shared/constants/incident'
import { CustomIncidentStatus } from '@libs/shared/constants/customIncident.enum'
import IncidentCustomIncidentData from '@infra/shared/data/incident-customIncident.data'
import CustomIncidentAndCheckData from '@infra/shared/data/customIncident-check.data'
import { IncidentEventService } from '@infra/domains/incidents/services/incident-event.service'

const pool = new Pool({
  port: process.env['DB_PORT'],
  host: process.env['DB_HOST'],
  user: process.env['DB_USERNAME'],
  password: process.env['DB_PASSWORD'],
  database: process.env['DB_DATABASE'],
  max: 1,
  min: 0,
  idleTimeoutMillis: 120000,
  connectionTimeoutMillis: 10000,
})

export async function handler(
  event: APIGatewayProxyEvent,
): Promise<APIGatewayProxyResult> {
  const id = event.pathParameters?.['id']

  if (!id) {
    return {
      statusCode: 400,
      body: JSON.stringify({ message: 'Missing id in path parameters' }),
    }
  }

  const client = await pool.connect()
  const customIncidentData = new CustomIncidentAndCheckData(client)
  try {
    const _updateCustomIncident = await customIncidentData.updateCustomIncident(
      id,
      {
        status: CustomIncidentStatus.UP,
      },
    )
    const triggeredCustomIncident =
      await IncidentCustomIncidentData.updateIncident(id, {
        status: IncidentStatus.RESOLVED,
      })
    const _triggeredCustomIncidentEvent =
      await IncidentEventService.createUserResolvedEvent(
        triggeredCustomIncident.id,
      )
    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Custom Incident resolved successfully',
        id,
      }),
    }
  } catch (error) {
    console.error(error)
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: 'Failed to resolve Custom Incident',
        error,
      }),
    }
  }
}
