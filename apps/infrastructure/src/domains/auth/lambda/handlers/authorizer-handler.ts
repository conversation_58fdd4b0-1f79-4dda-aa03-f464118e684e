import {
  APIGatewayTokenAuthorizerEvent,
  APIGatewayAuthorizerResult,
} from 'aws-lambda'
import { Pool } from 'pg'
import TeamData from '@infra/shared/data/team.data' // TODO: Update if data-handlers moves
import TokenData from '@infra/shared/data/token.data' // TODO: Update if data-handlers moves

import {
  QueryFunction,
  queryCustomIncident,
  queryCheck,
} from '../utils/resources-query'

// TODO: Remeber to change this to actual value when deploy
const pool = new Pool({
  port: process.env['DB_PORT'],
  host: process.env['DB_HOST'],
  user: process.env['DB_USERNAME'],
  password: process.env['DB_PASSWORD'],
  database: process.env['DB_DATABASE'],
  max: 1,
  min: 0,
  idleTimeoutMillis: 120000,
  connectionTimeoutMillis: 10000,
})

const QUERY_FUNCTIONS: Record<string, QueryFunction> = {
  check: queryCheck,
  customIncident: queryCustomIncident,
}

export async function handler(
  event: APIGatewayTokenAuthorizerEvent,
): Promise<APIGatewayAuthorizerResult> {
  const reqToken = event.authorizationToken
  const resource = event.methodArn.split('/')[3]
  const id = event.methodArn.split('/')[4]
  const client = await pool.connect()
  if (!id || !resource)
    throw Error(`Unauthorized: Resource Id not found "${resource}"`)
  try {
    const queryFunction = QUERY_FUNCTIONS[resource]
    if (!queryFunction) {
      throw new Error(`Unauthorized: Unsupported resource "${resource}"`)
    }
    const queriedResources = await queryFunction(client, id)

    if (!queriedResources) {
      throw new Error('Team not found (from customIncident)')
    }

    const teamData = new TeamData(client)
    const team = await teamData.getTeamById(queriedResources.teamId)
    if (!team) {
      throw new Error('Team not found in database')
    }

    const tokenData = new TokenData(client)
    const token = await tokenData.getTokenById(reqToken)
    if (!token) {
      throw new Error('Token not found')
    }

    if (token.level == 'team' && token.owner_id != team?.id) {
      throw new Error('Unauthorized, team token not match')
    } else if (
      token.level == 'organization' &&
      token.owner_id != team?.organization_id
    ) {
      throw new Error('Unauthorized, global token not match')
    }

    return {
      principalId: reqToken,
      policyDocument: {
        Version: '2012-10-17',
        Statement: [
          {
            Action: 'execute-api:Invoke',
            Effect: 'Allow',
            Resource: '*',
          },
        ],
      },
      context: {
        id: queriedResources.id,
        teamId: queriedResources.teamId,
        escalationPolicy: JSON.stringify(queriedResources['escalation']),
      },
    }
  } catch (error) {
    console.error(error)
    throw new Error('Unauthorized')
  } finally {
    client?.release(true)
  }
}
