import { SQSE<PERSON>, SQSRecord, Context } from 'aws-lambda'
import { Client } from 'pg'
import { UsageTypeUtils } from '@libs/shared/utils/usage-type.utils'

import * as crypto from 'crypto'

import { getDatabaseConfig, getStripeConfig } from '../utils/aws-clients'
import StripeUsageReportingData, {
  StripeDirectReportingMessage,
} from '../../../../shared/data/stripe-usage-reporting.data'

// Import dynamic utilities for usage type validation

/**
 * StripeDirectReportingLambda Handler
 *
 * Purpose: Consumes from StripeDirectReportingQueue, reports usage to <PERSON><PERSON>, logs to PostgreSQL
 * Trigger: SQS events from StripeDirectReportingQueue
 *
 * Functionality:
 * 1. Parse SQS messages containing billable transactional usage events
 * 2. Report usage to Stripe Meter Events API
 * 3. Create SubscriptionUsage records in PostgreSQL
 * 4. Handle errors and retries appropriately
 */
export const handler = async (
  event: SQSEvent,
  context: Context,
): Promise<void> => {
  console.log('StripeDirectReportingLambda triggered', {
    requestId: context.awsRequestId,
    messageCount: event.Records.length,
    timestamp: new Date().toISOString(),
  })

  // Process each SQS record
  for (const record of event.Records) {
    try {
      await processStripeDirectReportingRecord(record, context)
    } catch (error) {
      console.error('Failed to process record', {
        requestId: context.awsRequestId,
        messageId: record.messageId,
        error: error instanceof Error ? error.message : 'Unknown error',
        record: record.body,
      })

      // Re-throw error to trigger SQS retry mechanism
      throw error
    }
  }

  console.log('StripeDirectReportingLambda completed successfully', {
    requestId: context.awsRequestId,
    processedCount: event.Records.length,
  })
}

/**
 * Process individual SQS record for Stripe direct reporting
 */
async function processStripeDirectReportingRecord(
  record: SQSRecord,
  context: Context,
): Promise<void> {
  console.log('Processing Stripe direct reporting record', {
    requestId: context.awsRequestId,
    messageId: record.messageId,
    receiptHandle: record.receiptHandle,
  })

  // Parse message body
  let message: StripeDirectReportingMessage
  try {
    message = JSON.parse(record.body)
    validateStripeDirectReportingMessage(message)
  } catch (error) {
    console.error('Invalid message format', {
      requestId: context.awsRequestId,
      messageId: record.messageId,
      body: record.body,
      error: error instanceof Error ? error.message : 'Parse error',
    })
    throw new Error(
      `Invalid message format: ${error instanceof Error ? error.message : 'Unknown error'}`,
    )
  }

  // Report to Stripe Meter Events API (PRD FR2.5.2.2)
  const stripeUsageRecordId = await reportUsageToStripe(message, context)

  let reportingError: Error | null = null
  if (stripeUsageRecordId === null) {
    reportingError = new Error('Failed to report usage to Stripe')
  }

  // Create SubscriptionUsage record (PRD FR2.5.2.3)
  await createSubscriptionUsageRecord(
    message,
    context,
    stripeUsageRecordId,
    reportingError,
  )

  console.log('Successfully processed Stripe direct reporting record', {
    requestId: context.awsRequestId,
    messageId: record.messageId,
    organizationId: message.organizationId,
    usageType: message.usageType,
    usageValue: message.usageValue,
  })
}

/**
 * Validate Stripe direct reporting message structure
 */
function validateStripeDirectReportingMessage(
  message: any,
): asserts message is StripeDirectReportingMessage {
  const required = [
    'organizationId',
    'stripeCustomerId',
    'stripePriceId',
    'usageType',
    'usageValue',
    'eventTimestamp',
  ]

  for (const field of required) {
    if (!message[field]) {
      throw new Error(`Missing required field: ${field}`)
    }
  }

  // Dynamic usage type validation - support both transactional and overage types
  if (
    !UsageTypeUtils.isTransactionalUsageType(message.usageType) &&
    !UsageTypeUtils.isOverageUsageType(message.usageType)
  ) {
    throw new Error(
      `Invalid usageType: ${message.usageType}. Must be transactional (${UsageTypeUtils.getTransactionalUsageTypes().join(', ')}) or overage type`,
    )
  }

  if (typeof message.usageValue !== 'number' || message.usageValue <= 0) {
    throw new Error(`Invalid usageValue: ${message.usageValue}`)
  }

  // Validate timestamp format
  if (isNaN(Date.parse(message.eventTimestamp))) {
    throw new Error(`Invalid eventTimestamp format: ${message.eventTimestamp}`)
  }
}

/**
 * Report usage to Stripe Meter Events API
 */
async function reportUsageToStripe(
  message: StripeDirectReportingMessage,
  context: Context,
): Promise<string | null> {
  console.log('Reporting usage to Stripe', {
    requestId: context.awsRequestId,
    stripeCustomerId: message.stripeCustomerId,
    stripePriceId: message.stripePriceId,
    usageType: message.usageType,
    usageValue: message.usageValue,
  })

  try {
    // Get Stripe configuration
    const stripeConfig = getStripeConfig()
    const Stripe = (await import('stripe')).default
    const stripe = new Stripe(stripeConfig.secretKey, {
      apiVersion: '2025-02-24.acacia',
      timeout: parseInt(process.env['STRIPE_API_TIMEOUT'] || '30000'),
    })

    // Generate idempotency key for this usage event
    const idempotencyKey = generateIdempotencyKey(
      message.stripeCustomerId,
      message.usageType,
      message.eventTimestamp,
    )

    // Report usage to Stripe Meter Events API (PRD FR2.7.6)
    const eventName = `transactional_${message.usageType.toLowerCase()}`
    const timestamp = Math.floor(Date.parse(message.eventTimestamp) / 1000)

    const meterEvent = await stripe.billing.meterEvents.create(
      {
        event_name: eventName,
        payload: {
          stripe_customer_id: message.stripeCustomerId,
          value: message.usageValue.toString(),
          timestamp: timestamp.toString(),
        },
      },
      {
        idempotencyKey: idempotencyKey,
      },
    )

    console.log('Successfully reported usage to Stripe', {
      requestId: context.awsRequestId,
      meterEventId: meterEvent.identifier,
      eventName,
      timestamp,
    })

    return meterEvent.identifier
  } catch (error) {
    console.error('Failed to report usage to Stripe', {
      requestId: context.awsRequestId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stripeCustomerId: message.stripeCustomerId,
      usageType: message.usageType,
    })

    // Return null to indicate failure - will be handled by caller
    return null
  }
}

/**
 * Create SubscriptionUsage record in PostgreSQL (PRD FR2.5.2.3)
 */
async function createSubscriptionUsageRecord(
  message: StripeDirectReportingMessage,
  context: Context,
  stripeUsageRecordId: string | null = null,
  error: Error | null = null,
): Promise<void> {
  console.log('Creating subscription usage record', {
    requestId: context.awsRequestId,
    organizationId: message.organizationId,
    usageType: message.usageType,
    usageValue: message.usageValue,
    reportedToStripe: stripeUsageRecordId !== null,
    hasError: error !== null,
  })

  let client: Client | null = null
  try {
    // Get database connection details
    const dbConfig = getDatabaseConfig()
    client = new Client({
      host: dbConfig.host,
      port: dbConfig.port,
      database: dbConfig.database,
      user: dbConfig.username,
      password: dbConfig.password,
      ssl:
        process.env['NODE_ENV'] === 'production'
          ? { rejectUnauthorized: false }
          : false,
    })

    await client.connect()

    // Use data layer for subscription and usage operations
    const usageReportingData = new StripeUsageReportingData(client)

    // Find active subscription for organization
    const subscriptionInfo = await usageReportingData.findActiveSubscription(
      message.organizationId,
    )

    if (!subscriptionInfo) {
      console.warn('No active subscription found for organization', {
        requestId: context.awsRequestId,
        organizationId: message.organizationId,
      })
      return
    }

    // Create subscription usage record using data layer
    await usageReportingData.createSubscriptionUsageRecord(
      message,
      subscriptionInfo,
      stripeUsageRecordId,
      error,
    )

    console.log('Successfully created subscription usage record', {
      requestId: context.awsRequestId,
      organizationId: message.organizationId,
      usageType: message.usageType,
      reportedToStripe: stripeUsageRecordId !== null,
    })
  } catch (dbError) {
    console.error('Failed to create subscription usage record', {
      requestId: context.awsRequestId,
      organizationId: message.organizationId,
      error:
        dbError instanceof Error ? dbError.message : 'Unknown database error',
    })

    // Don't re-throw as this is a logging operation and shouldn't block the main flow
  } finally {
    if (client) {
      await client.end()
    }
  }
}

/**
 * Generate idempotency key for Stripe API calls
 */
function generateIdempotencyKey(
  stripeCustomerId: string,
  usageType: string,
  eventTimestamp: string,
): string {
  // Create deterministic key based on customer, usage type, and timestamp
  // This ensures the same event processed multiple times uses the same key
  const baseString = `${stripeCustomerId}-${usageType}-${eventTimestamp}`

  // Use a simple hash to create a shorter key (Stripe has limits on idempotency key length)
  const hash = crypto.createHash('sha256').update(baseString).digest('hex')

  return `billing-${hash.substring(0, 32)}`
}

// Note: Database and Stripe configuration functions are now imported from aws-clients.ts
