import * as cdk from 'aws-cdk-lib'
import { Construct } from 'constructs'
import * as sns from 'aws-cdk-lib/aws-sns'
import * as sqs from 'aws-cdk-lib/aws-sqs'
import * as lambda from 'aws-cdk-lib/aws-lambda'
import * as iam from 'aws-cdk-lib/aws-iam'
import * as logs from 'aws-cdk-lib/aws-logs'
import * as cloudwatch from 'aws-cdk-lib/aws-cloudwatch'
import * as actions from 'aws-cdk-lib/aws-cloudwatch-actions'
import * as lambdaEventSources from 'aws-cdk-lib/aws-lambda-event-sources'
import * as events from 'aws-cdk-lib/aws-events'
import { StringParameter } from 'aws-cdk-lib/aws-ssm'
import { StackProps } from '@infra/app'
import { StandardSQSQueueConstruct } from '@infra/shared/constructs/standard-sqs-queue.construct'
import { StandardLambdaConstruct } from '@infra/shared/constructs/standard-lambda.construct'
import { SecretProvider } from '@infra/shared/constructs/secret-provider.construct'
import { Environment } from '@infra/shared/environment'

export interface BillingStackProps extends StackProps {
  eventBusArn: string
  rdsClusterArn: string
  rdsSecretArn: string
  rdsDatabaseName: string
}

interface BillingQueues {
  stripeDirectReporting: StandardSQSQueueConstruct
  resourceManipulation: StandardSQSQueueConstruct
  eventEnrichment: StandardSQSQueueConstruct
}

interface BillingLambdas {
  stripeDirectReporting: StandardLambdaConstruct
  overageCalculation: StandardLambdaConstruct
  eventEnrichment: StandardLambdaConstruct
}

interface LambdaConfig {
  name: string
  entry: string
  handler: string
  memorySize: number
  timeout: cdk.Duration
  environment: { [key: string]: string }
  additionalPolicies: iam.PolicyStatement[]
  queue: sqs.IQueue
  dlq: sqs.IQueue
  batchSize: number
  maxBatchingWindow: cdk.Duration
}

export class BillingStack extends cdk.Stack {
  // Essential public interface - only expose core queues
  public readonly stripeDirectReportingQueue: sqs.Queue
  public readonly resourceManipulationQueue: sqs.Queue

  // Internal components
  private readonly queues: BillingQueues
  private readonly lambdas: BillingLambdas
  private readonly alarmsTopic: sns.Topic
  private readonly secretProvider: SecretProvider

  constructor(scope: Construct, id: string, props: BillingStackProps) {
    super(scope, id, props)

    // Initialize shared components
    this.secretProvider = new SecretProvider(this, 'BillingSecrets', {
      environment: props.environment,
    })

    // Create all resources using factory patterns
    this.queues = this.createAllQueues(props.environment)
    this.lambdas = this.createAllLambdas(props)
    this.alarmsTopic = this.createMonitoring(props.environment)
    this.createEventBridgeIntegration(props)

    // Create configuration
    this.createConfiguration(props.environment)
    this.createOutputs(props.environment)

    // Expose essential queues
    this.stripeDirectReportingQueue = this.queues.stripeDirectReporting.queue
    this.resourceManipulationQueue = this.queues.resourceManipulation.queue
  }

  /**
   * Factory method to create all SQS queues with consistent configuration
   */
  private createAllQueues(environment: string): BillingQueues {
    return {
      stripeDirectReporting: new StandardSQSQueueConstruct(
        this,
        'StripeDirectReportingQueue',
        {
          queueName: 'stripe-direct-reporting',
          environment,
          serviceName: 'Billing',
          purpose: 'StripeDirectReporting',
          visibilityTimeout: cdk.Duration.seconds(300),
          messageRetentionPeriod: cdk.Duration.days(14),
          maxReceiveCount: 3,
          highDepthThreshold: 50,
          oldMessageThreshold: 180,
          additionalTags: {
            DataSensitivity: 'High',
            BusinessCritical: 'True',
            Component: 'TransactionalBilling',
            Integration: 'Lambda',
          },
        },
      ),

      resourceManipulation: new StandardSQSQueueConstruct(
        this,
        'ResourceManipulationQueue',
        {
          queueName: 'resource-manipulation',
          environment,
          serviceName: 'Billing',
          purpose: 'ResourceManipulation',
          visibilityTimeout: cdk.Duration.seconds(180),
          messageRetentionPeriod: cdk.Duration.days(14),
          maxReceiveCount: 3,
          highDepthThreshold: 100,
          oldMessageThreshold: 300,
          additionalTags: {
            DataSensitivity: 'Medium',
            BusinessCritical: 'True',
            Component: 'ResourceOverage',
            Integration: 'Lambda',
          },
        },
      ),

      eventEnrichment: new StandardSQSQueueConstruct(
        this,
        'EventEnrichmentQueue',
        {
          queueName: `billing-event-enrichment-${environment}`,
          environment,
          serviceName: 'Billing',
          purpose: 'BillingEnrichment',
          visibilityTimeout: cdk.Duration.seconds(300),
          messageRetentionPeriod: cdk.Duration.days(14),
          maxReceiveCount: 3,
          highDepthThreshold: 50,
          oldMessageThreshold: 300,
          additionalTags: {
            DataSensitivity: 'Medium',
            BusinessCritical: 'True',
            Component: 'EventEnrichment',
            Integration: 'Lambda',
          },
        },
      ),
    }
  }

  /**
   * Factory method to create all Lambda functions with unified configuration
   */
  private createAllLambdas(props: BillingStackProps): BillingLambdas {
    // Shared IAM policies
    const commonPolicies = this.createCommonPolicies()

    // Lambda configurations
    const lambdaConfigs: LambdaConfig[] = [
      {
        name: `${props.environment}-stripe-direct-reporting`,
        entry: `${__dirname}/../../lambda/handlers/stripe-direct-reporting-handler.ts`,
        handler: 'handler',
        memorySize: 512,
        timeout: cdk.Duration.minutes(5),
        environment: this.createStripeDirectReportingEnvironment(
          props.environment,
        ),
        additionalPolicies: [...commonPolicies],
        queue: this.queues.stripeDirectReporting.queue,
        dlq: this.queues.stripeDirectReporting.deadLetterQueue as sqs.IQueue,
        batchSize: 10,
        maxBatchingWindow: cdk.Duration.seconds(5),
      },
      {
        name: `${props.environment}-overage-calculation`,
        entry: `${__dirname}/../../lambda/handlers/overage-calculation-handler.ts`,
        handler: 'handler',
        memorySize: 512,
        timeout: cdk.Duration.minutes(3),
        environment: this.createOverageCalculationEnvironment(
          props.environment,
        ),
        additionalPolicies: [
          ...commonPolicies,
          ...this.createDynamoDBPolicies(props.environment),
        ],
        queue: this.queues.resourceManipulation.queue,
        dlq: this.queues.resourceManipulation.deadLetterQueue as sqs.IQueue,
        batchSize: 5,
        maxBatchingWindow: cdk.Duration.seconds(10),
      },
      {
        name: `billing-event-enrichment-${props.environment}`,
        entry: `${__dirname}/../../lambda/handlers/event-enrichment-handler.ts`,
        handler: 'handler',
        memorySize: 1024,
        timeout: cdk.Duration.minutes(5),
        environment: this.createEventEnrichmentEnvironment(props),
        additionalPolicies: [
          ...commonPolicies,
          new iam.PolicyStatement({
            effect: iam.Effect.ALLOW,
            actions: [
              'rds-data:ExecuteStatement',
              'rds-data:BatchExecuteStatement',
              'rds-data:BeginTransaction',
              'rds-data:CommitTransaction',
              'rds-data:RollbackTransaction',
            ],
            resources: [props.rdsClusterArn],
          }),
          new iam.PolicyStatement({
            effect: iam.Effect.ALLOW,
            actions: ['events:PutEvents'],
            resources: [props.eventBusArn],
          }),
        ],
        queue: this.queues.eventEnrichment.queue,
        dlq: this.queues.eventEnrichment.deadLetterQueue as sqs.IQueue,
        batchSize: 10,
        maxBatchingWindow: cdk.Duration.seconds(5),
      },
    ]

    // Create all lambdas using factory pattern
    const lambdas = lambdaConfigs.map((config, index) =>
      this.createBillingLambda(config, index),
    )

    // Create log groups
    this.createLogGroups(lambdas, props.environment)

    return {
      stripeDirectReporting: lambdas[0] as StandardLambdaConstruct,
      overageCalculation: lambdas[1] as StandardLambdaConstruct,
      eventEnrichment: lambdas[2] as StandardLambdaConstruct,
    }
  }

  /**
   * Unified Lambda creation factory with consistent configuration
   */
  private createBillingLambda(
    config: LambdaConfig,
    index: number,
  ): StandardLambdaConstruct {
    const constructIds = [
      'StripeDirectReportingLambda',
      'OverageCalculationLambda',
      'EventEnrichmentLambda',
    ]

    const lambdaConstruct = new StandardLambdaConstruct(
      this,
      constructIds[index] as string,
      {
        entry: config.entry,
        handler: config.handler,
        functionName: config.name,
        runtime: lambda.Runtime.NODEJS_20_X,
        memorySize: config.memorySize,
        timeout: config.timeout,
        environment: config.environment,
        deadLetterQueue: config.dlq,
        additionalPolicies: config.additionalPolicies,
        logRetention: logs.RetentionDays.ONE_MONTH,
      },
    )

    // Grant secret access
    this.secretProvider.grantReadPermissionsTo(lambdaConstruct.lambdaFunction)

    // Configure SQS trigger
    lambdaConstruct.lambdaFunction.addEventSource(
      new lambdaEventSources.SqsEventSource(config.queue, {
        batchSize: config.batchSize,
        maxBatchingWindow: config.maxBatchingWindow,
        reportBatchItemFailures: true,
        enabled: true,
      }),
    )

    return lambdaConstruct
  }

  /**
   * Create common IAM policies shared across Lambda functions
   */
  private createCommonPolicies(): iam.PolicyStatement[] {
    return [
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          'sqs:ReceiveMessage',
          'sqs:DeleteMessage',
          'sqs:GetQueueAttributes',
          'sqs:ChangeMessageVisibility',
        ],
        resources: [
          this.queues.stripeDirectReporting.queue.queueArn,
          (this.queues.stripeDirectReporting.deadLetterQueue as sqs.IQueue)
            .queueArn,
          this.queues.resourceManipulation.queue.queueArn,
          (this.queues.resourceManipulation.deadLetterQueue as sqs.IQueue)
            .queueArn,
          this.queues.eventEnrichment.queue.queueArn,
          (this.queues.eventEnrichment.deadLetterQueue as sqs.IQueue).queueArn,
        ],
      }),
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: ['rds:DescribeDBInstances', 'rds-db:connect'],
        resources: ['*'],
      }),
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          'logs:CreateLogGroup',
          'logs:CreateLogStream',
          'logs:PutLogEvents',
        ],
        resources: ['*'],
      }),
    ]
  }

  /**
   * Create DynamoDB IAM policies for status page counting
   */
  private createDynamoDBPolicies(environment: string): iam.PolicyStatement[] {
    return [
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: ['dynamodb:Scan', 'dynamodb:Query', 'dynamodb:GetItem'],
        resources: [
          `arn:aws:dynamodb:*:*:table/${environment}-status-pages`,
          `arn:aws:dynamodb:*:*:table/${environment}-status-pages/*`,
        ],
      }),
    ]
  }

  /**
   * Create environment variables for StripeDirectReportingLambda
   */
  private createStripeDirectReportingEnvironment(environment: string): {
    [key: string]: string
  } {
    return {
      ENVIRONMENT: environment,
      NODE_ENV: this.secretProvider.nodeEnv,
      DB_HOST: this.secretProvider.dbHost,
      DB_PORT: this.secretProvider.dbPort,
      DB_NAME: this.secretProvider.dbName,
      DB_USERNAME: this.secretProvider.dbUsername,
      DB_PASSWORD: this.secretProvider.dbPassword,
      STRIPE_SECRET_KEY: this.secretProvider.stripeSecretKey,
      STRIPE_SMS_PRICE_ID: this.secretProvider.stripeSmsPriceId,
      STRIPE_VOICE_PRICE_ID: this.secretProvider.stripeVoicePriceId,
      LAMBDA_FUNCTION_NAME: `${environment}-stripe-direct-reporting`,
      LOG_LEVEL: this.secretProvider.appLogLevel,
      STRIPE_API_TIMEOUT: '30000',
      DB_CONNECTION_TIMEOUT: '10000',
      MAX_RETRIES: '3',
    }
  }

  /**
   * Create environment variables for OverageCalculationLambda
   */
  private createOverageCalculationEnvironment(environment: string): {
    [key: string]: string
  } {
    return {
      ENVIRONMENT: environment,
      NODE_ENV: this.secretProvider.nodeEnv,
      DB_HOST: this.secretProvider.dbHost,
      DB_PORT: this.secretProvider.dbPort,
      DB_NAME: this.secretProvider.dbName,
      DB_USERNAME: this.secretProvider.dbUsername,
      DB_PASSWORD: this.secretProvider.dbPassword,
      STRIPE_SECRET_KEY: this.secretProvider.stripeSecretKey,
      LAMBDA_FUNCTION_NAME: `${environment}-overage-calculation`,
      LOG_LEVEL: this.secretProvider.appLogLevel,
      STRIPE_API_TIMEOUT: '30000',
      DB_CONNECTION_TIMEOUT: '10000',
      MAX_RETRIES: '3',
      OVERAGE_CALCULATION_BATCH_SIZE: '100',
      OVERAGE_GRACE_PERIOD_HOURS: '24',
      // Add DynamoDB table name for status page counting
      STATUS_PAGES_TABLE_NAME: `${environment}-status-pages`,
    }
  }

  /**
   * Create environment variables for EventEnrichmentLambda
   */
  private createEventEnrichmentEnvironment(props: BillingStackProps): {
    [key: string]: string
  } {
    return {
      NODE_ENV: this.secretProvider.nodeEnv,
      LOG_LEVEL: this.secretProvider.appLogLevel,
      DB_HOST: this.secretProvider.dbHost,
      DB_PORT: this.secretProvider.dbPort,
      DB_NAME: this.secretProvider.dbName,
      DB_USERNAME: this.secretProvider.dbUsername,
      DB_PASSWORD: this.secretProvider.dbPassword,
      STRIPE_SMS_PRICE_ID: this.secretProvider.stripeSmsPriceId,
      STRIPE_VOICE_PRICE_ID: this.secretProvider.stripeVoicePriceId,
      EVENT_BUS_NAME: events.EventBus.fromEventBusArn(
        this,
        'ImportedEventBus',
        props.eventBusArn,
      ).eventBusName,
      // Add DynamoDB table name for status page counting
      STATUS_PAGES_TABLE_NAME: `${props.environment}-status-pages`,
    }
  }

  /**
   * Create CloudWatch log groups for Lambda functions
   */
  private createLogGroups(
    lambdas: StandardLambdaConstruct[],
    environment: string,
  ): void {
    lambdas.forEach((lambdaConstruct, index) => {
      const logGroupNames = [
        'StripeDirectReporting',
        'OverageCalculation',
        'EventEnrichment',
      ]

      new logs.LogGroup(this, `${logGroupNames[index] as string}LogGroup`, {
        logGroupName: `/aws/lambda/${lambdaConstruct.lambdaFunction.functionName}`,
        retention:
          environment === Environment.Prod
            ? logs.RetentionDays.THREE_MONTHS
            : logs.RetentionDays.ONE_WEEK,
        removalPolicy: cdk.RemovalPolicy.DESTROY,
      })
    })
  }

  /**
   * Create consolidated monitoring and alarms
   */
  private createMonitoring(environment: string): sns.Topic {
    const alarmsTopic = new sns.Topic(this, 'BillingAlarmsTopic', {
      topicName: `${environment}-billing-alarms`,
      displayName: 'Billing System Monitoring Alarms',
    })

    const snsAction = new actions.SnsAction(alarmsTopic)

    // Configure queue alarms
    Object.values(this.queues).forEach((queueConstruct) => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      queueConstruct.alarms.forEach((alarm: any) => {
        alarm.addAlarmAction(snsAction)
      })
    })

    // Create Lambda alarms
    Object.entries(this.lambdas).forEach(([name, lambdaConstruct]) => {
      this.createLambdaAlarms(
        lambdaConstruct.lambdaFunction,
        name,
        environment,
        snsAction,
      )
    })

    return alarmsTopic
  }

  /**
   * Create CloudWatch alarms for a Lambda function
   */
  private createLambdaAlarms(
    lambdaFunction: lambda.IFunction,
    functionName: string,
    environment: string,
    snsAction: actions.SnsAction,
  ): void {
    // Error rate alarm
    const errorAlarm = new cloudwatch.Alarm(this, `${functionName}ErrorRate`, {
      alarmName: `${environment}-${functionName}-ErrorRate`,
      alarmDescription: `High error rate for ${functionName} Lambda function`,
      metric: lambdaFunction.metricErrors({
        period: cdk.Duration.minutes(5),
        statistic: 'Sum',
      }),
      threshold: 5,
      evaluationPeriods: 1,
    })
    errorAlarm.addAlarmAction(snsAction)

    // Duration alarm
    const durationAlarm = new cloudwatch.Alarm(
      this,
      `${functionName}Duration`,
      {
        alarmName: `${environment}-${functionName}-Duration`,
        alarmDescription: `High duration for ${functionName} Lambda function`,
        metric: lambdaFunction.metricDuration({
          period: cdk.Duration.minutes(5),
          statistic: 'Average',
        }),
        threshold: 60000,
        evaluationPeriods: 2,
      },
    )
    durationAlarm.addAlarmAction(snsAction)

    // Throttle alarm
    const throttleAlarm = new cloudwatch.Alarm(
      this,
      `${functionName}Throttles`,
      {
        alarmName: `${environment}-${functionName}-Throttles`,
        alarmDescription: `Throttling detected for ${functionName} Lambda function`,
        metric: lambdaFunction.metricThrottles({
          period: cdk.Duration.minutes(5),
          statistic: 'Sum',
        }),
        threshold: 1,
        evaluationPeriods: 1,
      },
    )
    throttleAlarm.addAlarmAction(snsAction)
  }

  /**
   * Create EventBridge integration for enriched event forwarding
   */
  private createEventBridgeIntegration(props: BillingStackProps): events.Rule {
    const eventBus = events.EventBus.fromEventBusArn(
      this,
      'EventBusForEnrichment',
      props.eventBusArn,
    )

    return new events.Rule(this, 'EnrichmentForwardingRule', {
      ruleName: `billing-enrichment-forwarding-${props.environment}`,
      description:
        'Forwards enriched notification events to downstream systems',
      eventBus: eventBus,
      eventPattern: {
        source: ['monitoring-dog.billing.enrichment'],
        detailType: ['Notification Approved', 'Notification Blocked'],
      },
    })
  }

  /**
   * Create SSM parameters for Stripe configuration
   */
  private createConfiguration(environment: string): void {
    new StringParameter(this, 'StripeSMSPriceId', {
      parameterName: `/monitoring-dog/${environment}/stripe/sms-price-id`,
      stringValue:
        process.env['STRIPE_SMS_PRICE_ID'] || 'price_PLACEHOLDER_SMS',
      description: 'Stripe price ID for SMS usage billing',
      tier: cdk.aws_ssm.ParameterTier.STANDARD,
    })

    new StringParameter(this, 'StripeVoicePriceId', {
      parameterName: `/monitoring-dog/${environment}/stripe/voice-price-id`,
      stringValue:
        process.env['STRIPE_VOICE_PRICE_ID'] || 'price_PLACEHOLDER_VOICE',
      description: 'Stripe price ID for voice call usage billing',
      tier: cdk.aws_ssm.ParameterTier.STANDARD,
    })
  }

  /**
   * Create CloudFormation outputs
   */
  private createOutputs(environment: string): void {
    // SNS Topic output
    new cdk.CfnOutput(this, 'BillingAlarmsTopicArn', {
      value: this.alarmsTopic.topicArn,
      description: 'SNS Topic ARN for billing system alarms',
      exportName: `${environment}-BillingAlarmsTopicArn`,
    })

    // System integration output
    new cdk.CfnOutput(this, 'BillingSystemIntegrationInfo', {
      value: JSON.stringify({
        queues: {
          stripeDirectReporting: {
            queueUrl: this.queues.stripeDirectReporting.queue.queueUrl,
            queueArn: this.queues.stripeDirectReporting.queue.queueArn,
          },
          resourceManipulation: {
            queueUrl: this.queues.resourceManipulation.queue.queueUrl,
            queueArn: this.queues.resourceManipulation.queue.queueArn,
          },
          eventEnrichment: {
            queueUrl: this.queues.eventEnrichment.queue.queueUrl,
            queueArn: this.queues.eventEnrichment.queue.queueArn,
          },
        },
        lambdas: {
          stripeDirectReporting:
            this.lambdas.stripeDirectReporting.lambdaFunction.functionName,
          overageCalculation:
            this.lambdas.overageCalculation.lambdaFunction.functionName,
          eventEnrichment:
            this.lambdas.eventEnrichment.lambdaFunction.functionName,
        },
        alarmsTopicArn: this.alarmsTopic.topicArn,
        environment: environment,
      }),
      description: 'Complete billing system integration information',
    })

    // Summary output
    new cdk.CfnOutput(this, 'BillingSystemSummary', {
      value: [
        `Environment: ${environment}`,
        `Stripe Direct Reporting Queue: ${this.queues.stripeDirectReporting.queue.queueName}`,
        `Resource Manipulation Queue: ${this.queues.resourceManipulation.queue.queueName}`,
        `Event Enrichment Queue: ${this.queues.eventEnrichment.queue.queueName}`,
        `Stripe Direct Reporting Lambda: ${this.lambdas.stripeDirectReporting.lambdaFunction.functionName}`,
        `Overage Calculation Lambda: ${this.lambdas.overageCalculation.lambdaFunction.functionName}`,
        `Event Enrichment Lambda: ${this.lambdas.eventEnrichment.lambdaFunction.functionName}`,
        `Monitoring Topic: ${this.alarmsTopic.topicName}`,
      ].join(' | '),
      description: 'Billing system operational summary',
    })
  }

  // Legacy getters for backward compatibility (if needed by other stacks)
  public get eventEnrichmentQueue() {
    return this.queues.eventEnrichment.queue
  }

  public get stripeDirectReportingLambda() {
    return this.lambdas.stripeDirectReporting.lambdaFunction
  }

  public get overageCalculationLambda() {
    return this.lambdas.overageCalculation.lambdaFunction
  }

  public get eventEnrichmentLambda() {
    return this.lambdas.eventEnrichment.lambdaFunction
  }
}
