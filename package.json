{"name": "@monitoring-dog/source", "version": "0.0.0", "license": "MIT", "scripts": {"prepare": "husky", "preinstall": "npx only-allow pnpm", "lint": "nx run-many --target=lint --all", "lint:fix": "nx run-many --target=lint --all --fix", "backend:build": "nx run user-portal-backend:build", "backend:dev": "nx run user-portal-backend:serve --configuration=development", "orch:dev": "nx run orchestrator:serve --configuration=development", "worker:dev": "nx run worker:start:multi --configuration=development", "typeorm": "typeorm-ts-node-commonjs", "pack-secrets": "sh ./secrets-scripts/pack-secrets.sh", "unpack-secrets": "sh ./secrets-scripts/unpack-secrets.sh", "firebase:start": "firebase emulators:start --import=firebase-data --export-on-exit=firebase-data", "firebase:auth": "node ./scripts/firebase-auth.mjs", "sync-env": "node ./scripts/sync-env.mjs", "migration:create": "nx run user-portal-backend:typeorm-migration-create", "migration:run": "nx run user-portal-backend:typeorm-migration-run"}, "private": true, "dependencies": {"dns-packet": "^5.6.1", "@anatine/zod-nestjs": "^2.0.9", "@anatine/zod-openapi": "^2.2.6", "@aws-sdk/client-dynamodb": "^3.632.0", "@aws-sdk/client-eventbridge": "^3.716.0", "@aws-sdk/client-sfn": "^3.637.0", "@aws-sdk/client-sqs": "^3.632.0", "@aws-sdk/client-ssm": "^3.628.0", "@aws-sdk/credential-providers": "^3.624.0", "@bull-board/api": "^5.21.4", "@bull-board/express": "^5.21.4", "@bull-board/fastify": "^5.21.4", "@bull-board/nestjs": "^5.21.4", "@casl/ability": "^6.7.1", "@faker-js/faker": "^8.4.1", "@influxdata/influxdb3-client": "^0.9.0", "@libsql/client": "^0.9.0", "@mswjs/http-middleware": "^0.10.1", "@nestjs/bullmq": "^10.1.1", "@nestjs/cli": "^10.4.4", "@nestjs/common": "^10.4.1", "@nestjs/config": "^3.2.2", "@nestjs/core": "^10.3.9", "@nestjs/event-emitter": "^2.0.4", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.3.9", "@nestjs/swagger": "^7.3.1", "@nestjs/terminus": "^11.0.0", "@nestjs/typeorm": "^10.0.2", "@opentelemetry/api": "~1.9.0", "@opentelemetry/exporter-metrics-otlp-grpc": "0.54.2", "@opentelemetry/resources": "1.26.0", "@opentelemetry/sdk-metrics": "1.26.0", "@opentelemetry/semantic-conventions": "1.26.0", "@react-email/components": "^0.0.28", "@slack/bolt": "^4.1.0", "@slack/web-api": "^7.3.4", "@slack/webhook": "^7.0.3", "@ssut/nestjs-sqs": "^3.0.0", "@szmarczak/http-timer": "^5.0.1", "aws-cdk-lib": "^2.151.0", "aws-sdk-v3-nest": "^0.3.0", "axios": "^1.6.0", "body-parser": "^1.20.2", "bullmq": "^5.10.4", "cache-manager": "^5.7.6", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "constructs": "^10.3.0", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "drizzle-orm": "^0.33.0", "dynamoose": "^4.0.1", "express": "^4.18.1", "firebase-admin": "^12.1.1", "ioredis": "^5.4.1", "lodash": "^4.17.21", "mjml": "^4.15.3", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "msw": "^2.3.5", "nanoid": "3", "nest-winston": "^1.10.0", "nestjs-dynamoose": "^0.5.8", "nestjs-firebase": "^10.5.0", "nestjs-request-context": "^3.0.0", "nodemailer": "^6.9.14", "nodemailer-mjml": "^1.3.5", "onesignal-api-client-core": "^1.2.2", "openapi3-ts": "^4.3.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.12.0", "ping": "^0.4.4", "plivo": "^4.69.5", "query-string": "^7.1.3", "react": "^18.3.1", "react-dom": "^18.3.1", "reflect-metadata": "^0.2.2", "rrule": "^2.8.1", "rxjs": "^7.8.1", "stripe": "^17.7.0", "tslib": "^2.6.3", "typeorm": "^0.3.20", "typeorm-naming-strategies": "^4.1.0", "uuid": "^11.0.3", "webpack": "^5.92.1", "winston": "^3.13.0", "yargs": "^17.7.2", "zod": "^3.23.8", "@aws-lambda-powertools/logger": "^1.17.0", "@aws-lambda-powertools/metrics": "^1.17.0", "@aws-lambda-powertools/tracer": "^1.17.0", "@aws-sdk/client-rds-data": "^3.450.0"}, "devDependencies": {"@nestjs/cli": "^10.3.2", "@nestjs/schematics": "^10.1.1", "@nestjs/testing": "^10.3.9", "@nx-tools/nx-container": "^6.0.1", "@nx/eslint": "19.6.0", "@nx/eslint-plugin": "19.6.0", "@nx/express": "19.6.0", "@nx/jest": "19.6.0", "@nx/js": "19.6.0", "@nx/nest": "19.6.0", "@nx/node": "19.6.0", "@nx/web": "19.6.0", "@nx/webpack": "19.6.0", "@nx/workspace": "19.6.0", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/aws-lambda": "^8.10.145", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/node": "^20.14.15", "@types/nodemailer": "^6.4.15", "@types/passport-jwt": "^4.0.1", "@types/pg": "^8.6.6", "@types/react": "^18.3.12", "@types/supertest": "^6.0.2", "@types/webpack-env": "^1.18.5", "@typescript-eslint/eslint-plugin": "^7.13.0", "@typescript-eslint/parser": "^7.13.0", "aws-cdk": "^2.151.0", "chalk": "^5.3.0", "commander": "^12.1.0", "cross-env": "^7.0.3", "dynamoose-logger": "^4.0.1", "envfile": "^7.1.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-unused-imports": "^3.2.0", "firebase": "^10.12.2", "fs-extra": "^11.2.0", "husky": "^9.1.4", "inquirer": "^9.2.23", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "lint-staged": "^15.2.7", "mysterio": "^2.1.0", "nx": "19.6.0", "nx-serverless-cdk": "^1.2.1", "prettier": "^3.3.2", "source-map-support": "^0.5.21", "strip-ansi": "7.1.0", "supertest": "^7.0.0", "ts-jest": "^29.1.4", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "~5.5.2", "webpack-cli": "^5.1.4", "webpack-node-externals": "^3.0.0"}, "lint-staged": {"*.{ts,tsx}": ["pnpm exec nx run-many --target=lint --all --fix"]}, "packageManager": "pnpm@9.9.0+sha256.7a4261e50d9a44d9240baf6c9d6e10089dcf0a79d0007f2a26985a6927324177"}